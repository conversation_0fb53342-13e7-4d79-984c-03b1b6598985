package common

import "regexp"

// ProcessItem represents a single process in the process tree.
type ProcessItem struct {
	Name     string         `json:"name"`
	Cmd      string         `json:"cmd"`
	PID      int            `json:"pid"`
	PPID     int            `json:"ppid"`
	Load     float64        `json:"load"`
	Mem      float64        `json:"mem"`
	Children []*ProcessItem `json:"children,omitempty"`
}

// CommandOptions to be passed to the external program or shell.
type CommandOptions struct {
	// The current working directory of the executed program or shell.
	Cwd string
	// The environment of the executed program or shell.
	Env map[string]string
}

// Executable represents a command to be executed.
type Executable struct {
	// The command to be executed.
	Command string
	// Specifies whether the command is a shell command.
	IsShellCommand bool
	// The arguments passed to the command.
	Args []string
	// The command options used when the command is executed.
	Options *CommandOptions
}

// ForkOptions specifies options for forking a process.
type ForkOptions struct {
	CommandOptions
	ExecArgv []string
}

// Source specifies the source of data.
type Source int

const (
	// Stdout is for standard output.
	Stdout Source = iota
	// Stderr is for standard error.
	Stderr
)

// SuccessData is the data send via a success callback.
type SuccessData struct {
	Error      error
	CmdCode    int
	Terminated bool
}

// ErrorData is the data send via an error callback.
type ErrorData struct {
	Error      error
	Terminated bool
	Stdout     string
	Stderr     string
}

// TerminateResponseCode defines the response code for a terminate request.
type TerminateResponseCode int

const (
	// TerminateResponseCodeSuccess means the process was terminated successfully.
	TerminateResponseCodeSuccess TerminateResponseCode = 0
	// TerminateResponseCodeUnknown means the process could not be terminated.
	TerminateResponseCodeUnknown TerminateResponseCode = 1
	// TerminateResponseCodeAccessDenied means access was denied to terminate the process.
	TerminateResponseCodeAccessDenied TerminateResponseCode = 2
	// TerminateResponseCodeProcessNotFound means the process was not found.
	TerminateResponseCodeProcessNotFound TerminateResponseCode = 3
)

// TerminateResponse is the response from a terminate request.
type TerminateResponse struct {
	Success bool
	Code    TerminateResponseCode
	Error   interface{}
}

// SanitizeProcessEnvironment sanitizes a VS Code process environment by removing all Electron/VS Code-related values.
func SanitizeProcessEnvironment(env map[string]string, preserve ...string) {
	preserveSet := make(map[string]bool)
	for _, key := range preserve {
		preserveSet[key] = true
	}

	keysToRemove := []*regexp.Regexp{
		regexp.MustCompile("^ELECTRON_.+$"),
		regexp.MustCompile("^VSCODE_(?!(PORTABLE|SHELL_LOGIN|ENV_REPLACE|ENV_APPEND|ENV_PREPEND)).+$"),
		regexp.MustCompile("^SNAP(|_.*)$"),
		regexp.MustCompile("^GDK_PIXBUF_.+$"),
	}

	for key := range env {
		if preserveSet[key] {
			continue
		}
		for _, re := range keysToRemove {
			if re.MatchString(key) {
				delete(env, key)
				break
			}
		}
	}
}

// RemoveDangerousEnvVariables removes dangerous environment variables that have caused crashes in forked processes.
func RemoveDangerousEnvVariables(env map[string]string) {
	if env == nil {
		return
	}
	delete(env, "DEBUG")
	// In the original TypeScript, this is guarded by `isLinux`.
	// We'll need to resolve the `platform` dependency to do the same.
	delete(env, "LD_PRELOAD")
}
