/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"net/url"
	"path"
	"strings"
)

// OriginalFSPath returns the original file system path from a URI
func OriginalFSPath(uri *URI) string {
	return uri.FSPath()
}

// JoinP<PERSON> joins a URI with path fragments (standalone function)
func JoinPath(resource *URI, pathFragments ...string) *URI {
	if resource == nil {
		return nil
	}

	joinedPath := resource.Path
	for _, fragment := range pathFragments {
		if !strings.HasSuffix(joinedPath, "/") && !strings.HasPrefix(fragment, "/") {
			joinedPath += "/"
		}
		joinedPath += fragment
	}

	return NewURI(resource.Scheme, resource.Authority, joinedPath, resource.Query, resource.Fragment)
}

// URIBasename returns the basename of a URI path (standalone function)
func URIBasename(resource *URI) string {
	if resource == nil {
		return ""
	}
	return path.Base(resource.Path)
}

// IExtUri interface for extended URI operations
type IExtUri interface {
	// Identity
	Compare(uri1, uri2 *URI, ignoreFragment ...bool) int
	IsEqual(uri1, uri2 *URI, ignoreFragment ...bool) bool
	IsEqualOrParent(base, parentCandidate *URI, ignoreFragment ...bool) bool
	GetComparisonKey(uri *URI, ignoreFragment ...bool) string
	IgnorePathCasing(uri *URI) bool

	// Path math
	BasenameOrAuthority(resource *URI) string
	Basename(resource *URI) string
	Extname(resource *URI) string
	Dirname(resource *URI) *URI
	JoinPath(resource *URI, pathFragments ...string) *URI
	NormalizePath(resource *URI) *URI
	RelativePath(from, to *URI) string
	ResolvePath(base *URI, pathStr string) *URI

	// Misc
	IsAbsolutePath(resource *URI) bool
	IsEqualAuthority(a1, a2 string) bool
	HasTrailingPathSeparator(resource *URI, sep ...string) bool
	RemoveTrailingPathSeparator(resource *URI, sep ...string) *URI
	AddTrailingPathSeparator(resource *URI, sep ...string) *URI
}

// ExtUri implements IExtUri
type ExtUri struct {
	ignorePathCasing func(*URI) bool
}

// NewExtUri creates a new ExtUri with the given path casing function
func NewExtUri(ignorePathCasing func(*URI) bool) *ExtUri {
	return &ExtUri{
		ignorePathCasing: ignorePathCasing,
	}
}

// Compare compares two URIs
func (e *ExtUri) Compare(uri1, uri2 *URI, ignoreFragment ...bool) int {
	if uri1 == uri2 {
		return 0
	}

	ignoreFragmentFlag := false
	if len(ignoreFragment) > 0 {
		ignoreFragmentFlag = ignoreFragment[0]
	}

	key1 := e.GetComparisonKey(uri1, ignoreFragmentFlag)
	key2 := e.GetComparisonKey(uri2, ignoreFragmentFlag)

	if key1 < key2 {
		return -1
	} else if key1 > key2 {
		return 1
	}
	return 0
}

// IsEqual tests whether two URIs are equal
func (e *ExtUri) IsEqual(uri1, uri2 *URI, ignoreFragment ...bool) bool {
	if uri1 == uri2 {
		return true
	}
	if uri1 == nil || uri2 == nil {
		return false
	}

	ignoreFragmentFlag := false
	if len(ignoreFragment) > 0 {
		ignoreFragmentFlag = ignoreFragment[0]
	}

	return e.GetComparisonKey(uri1, ignoreFragmentFlag) == e.GetComparisonKey(uri2, ignoreFragmentFlag)
}

// GetComparisonKey creates a key for URI comparison
func (e *ExtUri) GetComparisonKey(uri *URI, ignoreFragment ...bool) string {
	ignoreFragmentFlag := false
	if len(ignoreFragment) > 0 {
		ignoreFragmentFlag = ignoreFragment[0]
	}

	withOptions := &URIWithOptions{
		URI: uri,
	}

	if e.ignorePathCasing(uri) {
		withOptions.Path = strings.ToLower(uri.Path)
	}

	if ignoreFragmentFlag {
		withOptions.Fragment = ""
		withOptions.SetFragment = true
	}

	return withOptions.ToString()
}

// IgnorePathCasing returns whether path casing should be ignored for the URI
func (e *ExtUri) IgnorePathCasing(uri *URI) bool {
	return e.ignorePathCasing(uri)
}

// IsEqualOrParent tests whether a candidate URI is a parent or equal of a given base URI
func (e *ExtUri) IsEqualOrParent(base, parentCandidate *URI, ignoreFragment ...bool) bool {
	ignoreFragmentFlag := false
	if len(ignoreFragment) > 0 {
		ignoreFragmentFlag = ignoreFragment[0]
	}

	if base.Scheme == parentCandidate.Scheme {
		if base.Scheme == Schemas.File {
			baseFS := OriginalFSPath(base)
			parentFS := OriginalFSPath(parentCandidate)
			return IsEqualOrParent(baseFS, parentFS, e.ignorePathCasing(base)) &&
				base.Query == parentCandidate.Query &&
				(ignoreFragmentFlag || base.Fragment == parentCandidate.Fragment)
		}

		if IsEqualAuthority(base.Authority, parentCandidate.Authority) {
			return IsEqualOrParent(base.Path, parentCandidate.Path, e.ignorePathCasing(base), "/") &&
				base.Query == parentCandidate.Query &&
				(ignoreFragmentFlag || base.Fragment == parentCandidate.Fragment)
		}
	}
	return false
}

// JoinPath joins a URI path with path fragments
func (e *ExtUri) JoinPath(resource *URI, pathFragments ...string) *URI {
	joinedPath := resource.Path
	for _, fragment := range pathFragments {
		joinedPath = path.Join(joinedPath, fragment)
	}
	return NewURI(resource.Scheme, resource.Authority, joinedPath, resource.Query, resource.Fragment)
}

// BasenameOrAuthority returns the basename or authority
func (e *ExtUri) BasenameOrAuthority(resource *URI) string {
	basename := e.Basename(resource)
	if basename != "" {
		return basename
	}
	return resource.Authority
}

// Basename returns the basename of the URI path
func (e *ExtUri) Basename(resource *URI) string {
	return path.Base(resource.Path)
}

// Extname returns the extension of the URI path
func (e *ExtUri) Extname(resource *URI) string {
	return path.Ext(resource.Path)
}

// Dirname returns the directory of the URI
func (e *ExtUri) Dirname(resource *URI) *URI {
	if resource.Path == "/" || resource.Path == "" {
		return resource
	}

	dirPath := path.Dir(resource.Path)
	if dirPath == "." {
		dirPath = ""
	}

	return NewURI(resource.Scheme, resource.Authority, dirPath, resource.Query, resource.Fragment)
}

// NormalizePath normalizes the path part of a URI
func (e *ExtUri) NormalizePath(resource *URI) *URI {
	if resource.Path == "" || resource.Path == "/" {
		return resource
	}

	normalizedPath := path.Clean(resource.Path)

	// Preserve trailing slash if it was there
	if strings.HasSuffix(resource.Path, "/") && !strings.HasSuffix(normalizedPath, "/") {
		normalizedPath += "/"
	}

	return NewURI(resource.Scheme, resource.Authority, normalizedPath, resource.Query, resource.Fragment)
}

// RelativePath returns the relative path from one URI to another
func (e *ExtUri) RelativePath(from, to *URI) string {
	if from.Scheme != to.Scheme || !e.IsEqualAuthority(from.Authority, to.Authority) {
		return ""
	}

	if from.Path == to.Path {
		return ""
	}

	fromPath := from.Path
	toPath := to.Path

	// Simple relative path calculation
	if strings.HasPrefix(toPath, fromPath) {
		if strings.HasSuffix(fromPath, "/") {
			return toPath[len(fromPath):]
		} else {
			return toPath[len(fromPath)+1:]
		}
	}

	// More complex relative path calculation would go here
	// For now, return empty string for non-trivial cases
	return ""
}

// ResolvePath resolves an absolute or relative path against a base URI
func (e *ExtUri) ResolvePath(base *URI, pathStr string) *URI {
	if path.IsAbs(pathStr) {
		return NewURI(base.Scheme, base.Authority, pathStr, "", "")
	}

	// Join relative path
	resolvedPath := path.Join(base.Path, pathStr)

	return NewURI(base.Scheme, base.Authority, resolvedPath, "", "")
}

// IsAbsolutePath returns true if the URI path is absolute
func (e *ExtUri) IsAbsolutePath(resource *URI) bool {
	return path.IsAbs(resource.Path)
}

// IsEqualAuthority tests whether two authorities are the same
func (e *ExtUri) IsEqualAuthority(a1, a2 string) bool {
	return IsEqualAuthority(a1, a2)
}

// HasTrailingPathSeparator returns true if the URI path has a trailing separator
func (e *ExtUri) HasTrailingPathSeparator(resource *URI, sep ...string) bool {
	separator := "/"
	if len(sep) > 0 {
		separator = sep[0]
	}

	return strings.HasSuffix(resource.Path, separator)
}

// RemoveTrailingPathSeparator removes a trailing path separator if there's one
func (e *ExtUri) RemoveTrailingPathSeparator(resource *URI, sep ...string) *URI {
	separator := "/"
	if len(sep) > 0 {
		separator = sep[0]
	}

	if !strings.HasSuffix(resource.Path, separator) || resource.Path == separator {
		return resource
	}

	newPath := strings.TrimSuffix(resource.Path, separator)

	return NewURI(resource.Scheme, resource.Authority, newPath, resource.Query, resource.Fragment)
}

// AddTrailingPathSeparator adds a trailing path separator if there isn't one
func (e *ExtUri) AddTrailingPathSeparator(resource *URI, sep ...string) *URI {
	separator := "/"
	if len(sep) > 0 {
		separator = sep[0]
	}

	if strings.HasSuffix(resource.Path, separator) {
		return resource
	}

	newPath := resource.Path + separator

	return NewURI(resource.Scheme, resource.Authority, newPath, resource.Query, resource.Fragment)
}

// Global utility functions

// IsEqualAuthority checks if two authority strings are equal
func IsEqualAuthority(a1, a2 string) bool {
	return a1 == a2
}

// IsEqualOrParent checks if parent is equal or parent of child path
func IsEqualOrParent(child, parent string, ignoreCase bool, sep ...string) bool {
	separator := "/"
	if len(sep) > 0 {
		separator = sep[0]
	}

	if ignoreCase {
		child = strings.ToLower(child)
		parent = strings.ToLower(parent)
	}

	if child == parent {
		return true
	}

	if !strings.HasSuffix(parent, separator) {
		parent += separator
	}

	return strings.HasPrefix(child, parent)
}

// IsAbsolutePath checks if a path is absolute
func IsAbsolutePath(resource *URI) bool {
	return path.IsAbs(resource.Path)
}

// Global ExtUri instances

// extUri with case sensitive path comparison
var extUri = NewExtUri(func(uri *URI) bool {
	// For file scheme, use OS-specific case sensitivity
	// For other schemes, be case sensitive by default
	if uri.Scheme == Schemas.File {
		return IsWindows || !IsLinux
	}
	return false
})

// extUriIgnorePathCase with case insensitive path comparison
var extUriIgnorePathCase = NewExtUri(func(uri *URI) bool {
	return true
})

// GetExtUri returns the default ExtUri instance
func GetExtUri() IExtUri {
	return extUri
}

// GetExtUriIgnorePathCase returns the case-insensitive ExtUri instance
func GetExtUriIgnorePathCase() IExtUri {
	return extUriIgnorePathCase
}

// DistinctParents returns distinct parent URIs from a list of items
func DistinctParents[T any](items []T, resourceAccessor func(T) *URI) []T {
	if len(items) <= 1 {
		return items
	}

	var result []T
	parentPaths := make(map[string]bool)

	for _, item := range items {
		resource := resourceAccessor(item)
		parentPath := GetExtUri().Dirname(resource).ToString()

		if !parentPaths[parentPath] {
			parentPaths[parentPath] = true
			result = append(result, item)
		}
	}

	return result
}

// DataURI utility functions
type DataURI struct{}

// ParseMetaData parses metadata from a data URI
func (d *DataURI) ParseMetaData(dataUri *URI) map[string]string {
	result := make(map[string]string)

	if dataUri.Scheme != "data" {
		return result
	}

	// Simple parsing - in real implementation this would be more sophisticated
	pathParts := strings.Split(dataUri.Path, ",")
	if len(pathParts) > 0 {
		metaPart := pathParts[0]
		if strings.Contains(metaPart, ";") {
			parts := strings.Split(metaPart, ";")
			for _, part := range parts {
				if strings.Contains(part, "=") {
					kv := strings.SplitN(part, "=", 2)
					if len(kv) == 2 {
						key, _ := url.QueryUnescape(kv[0])
						value, _ := url.QueryUnescape(kv[1])
						result[key] = value
					}
				}
			}
		}
	}

	return result
}

// ToLocalResource converts a resource to a local resource
func ToLocalResource(resource *URI, authority string, localScheme string) *URI {
	if authority != "" && resource.Authority == authority {
		return NewURI(localScheme, "", resource.Path, resource.Query, resource.Fragment)
	}
	return resource
}

// URIWithOptions helper for creating modified URIs
type URIWithOptions struct {
	*URI
	Path        string
	SetFragment bool
	Fragment    string
}

// ToString converts the URI with options to string
func (u *URIWithOptions) ToString() string {
	result := u.URI.ToString()

	if u.Path != "" {
		// Replace path in the result
		parsed, err := url.Parse(result)
		if err == nil {
			parsed.Path = u.Path
			result = parsed.String()
		}
	}

	if u.SetFragment {
		// Replace fragment in the result
		parsed, err := url.Parse(result)
		if err == nil {
			parsed.Fragment = u.Fragment
			result = parsed.String()
		}
	}

	return result
}
