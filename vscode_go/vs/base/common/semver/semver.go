/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package semver

import (
	"fmt"
	"regexp"
	"sort"
	"strconv"
	"strings"
)

const SEMVER_SPEC_VERSION = "2.0.0"

type ReleaseType string

const (
	ReleaseMajor      ReleaseType = "major"
	ReleasePreMajor   ReleaseType = "premajor"
	ReleaseMinor      ReleaseType = "minor"
	ReleasePreMinor   ReleaseType = "preminor"
	ReleasePatch      ReleaseType = "patch"
	ReleasePrePatch   ReleaseType = "prepatch"
	ReleasePreRelease ReleaseType = "prerelease"
)

type Options struct {
	Loose             bool
	IncludePrerelease bool
}

type CoerceOptions struct {
	Options
	RTL bool
}

type Operator string

const (
	OpEqual          Operator = "="
	OpNotEqual       Operator = "!="
	OpGreater        Operator = ">"
	OpGreaterEqual   Operator = ">="
	OpLess           Operator = "<"
	OpLessEqual      Operator = "<="
	OpStrictEqual    Operator = "==="
	OpStrictNotEqual Operator = "!=="
	OpEmpty          Operator = ""
)

type SemVer struct {
	Raw        string
	Loose      bool
	Options    Options
	Major      int
	Minor      int
	Patch      int
	Version    string
	Build      []string
	Prerelease []string
}

var (
	// Regex patterns for parsing semver
	semverRegex = regexp.MustCompile(`^v?(\d+)\.(\d+)\.(\d+)(?:-([a-zA-Z0-9\-\.]+))?(?:\+([a-zA-Z0-9\-\.]+))?$`)
	looseRegex  = regexp.MustCompile(`^v?(\d+)(?:\.(\d+))?(?:\.(\d+))?(?:-([a-zA-Z0-9\-\.]+))?(?:\+([a-zA-Z0-9\-\.]+))?$`)
)

func Parse(version string, options *Options) *SemVer {
	if version == "" {
		return nil
	}

	version = strings.TrimSpace(version)
	if version == "" {
		return nil
	}

	opts := &Options{}
	if options != nil {
		*opts = *options
	}

	var regex *regexp.Regexp
	if opts.Loose {
		regex = looseRegex
	} else {
		regex = semverRegex
	}

	matches := regex.FindStringSubmatch(version)
	if matches == nil {
		return nil
	}

	major, err := strconv.Atoi(matches[1])
	if err != nil {
		return nil
	}

	minor := 0
	if matches[2] != "" {
		minor, err = strconv.Atoi(matches[2])
		if err != nil {
			return nil
		}
	}

	patch := 0
	if matches[3] != "" {
		patch, err = strconv.Atoi(matches[3])
		if err != nil {
			return nil
		}
	}

	var prerelease []string
	if matches[4] != "" {
		prerelease = strings.Split(matches[4], ".")
	}

	var build []string
	if matches[5] != "" {
		build = strings.Split(matches[5], ".")
	}

	versionStr := fmt.Sprintf("%d.%d.%d", major, minor, patch)
	if len(prerelease) > 0 {
		versionStr += "-" + strings.Join(prerelease, ".")
	}
	if len(build) > 0 {
		versionStr += "+" + strings.Join(build, ".")
	}

	return &SemVer{
		Raw:        version,
		Loose:      opts.Loose,
		Options:    *opts,
		Major:      major,
		Minor:      minor,
		Patch:      patch,
		Version:    versionStr,
		Build:      build,
		Prerelease: prerelease,
	}
}

func Valid(version string, options *Options) string {
	parsed := Parse(version, options)
	if parsed == nil {
		return ""
	}
	return parsed.Version
}

func Clean(version string, options *Options) string {
	version = strings.TrimSpace(version)
	version = strings.TrimPrefix(version, "=")
	version = strings.TrimPrefix(version, "v")
	return Valid(version, options)
}

func Major(version string, options *Options) int {
	parsed := Parse(version, options)
	if parsed == nil {
		return 0
	}
	return parsed.Major
}

func Minor(version string, options *Options) int {
	parsed := Parse(version, options)
	if parsed == nil {
		return 0
	}
	return parsed.Minor
}

func Patch(version string, options *Options) int {
	parsed := Parse(version, options)
	if parsed == nil {
		return 0
	}
	return parsed.Patch
}

func Prerelease(version string, options *Options) []string {
	parsed := Parse(version, options)
	if parsed == nil {
		return nil
	}
	return parsed.Prerelease
}

func Compare(v1, v2 string, options *Options) int {
	parsed1 := Parse(v1, options)
	parsed2 := Parse(v2, options)

	if parsed1 == nil && parsed2 == nil {
		return 0
	}
	if parsed1 == nil {
		return -1
	}
	if parsed2 == nil {
		return 1
	}

	return compareParsed(parsed1, parsed2)
}

func compareParsed(v1, v2 *SemVer) int {
	if v1.Major != v2.Major {
		if v1.Major > v2.Major {
			return 1
		}
		return -1
	}

	if v1.Minor != v2.Minor {
		if v1.Minor > v2.Minor {
			return 1
		}
		return -1
	}

	if v1.Patch != v2.Patch {
		if v1.Patch > v2.Patch {
			return 1
		}
		return -1
	}

	return comparePrerelease(v1.Prerelease, v2.Prerelease)
}

func comparePrerelease(pre1, pre2 []string) int {
	// If both are empty, they're equal
	if len(pre1) == 0 && len(pre2) == 0 {
		return 0
	}

	// If one is empty and the other isn't, the empty one is greater
	if len(pre1) == 0 {
		return 1
	}
	if len(pre2) == 0 {
		return -1
	}

	// Compare each part
	minLen := len(pre1)
	if len(pre2) < minLen {
		minLen = len(pre2)
	}

	for i := 0; i < minLen; i++ {
		p1 := pre1[i]
		p2 := pre2[i]

		// Try to parse as numbers
		n1, err1 := strconv.Atoi(p1)
		n2, err2 := strconv.Atoi(p2)

		if err1 == nil && err2 == nil {
			// Both are numbers
			if n1 != n2 {
				if n1 > n2 {
					return 1
				}
				return -1
			}
		} else if err1 == nil {
			// p1 is number, p2 is string - number is less
			return -1
		} else if err2 == nil {
			// p2 is number, p1 is string - number is less
			return 1
		} else {
			// Both are strings
			if p1 != p2 {
				if p1 > p2 {
					return 1
				}
				return -1
			}
		}
	}

	// If we get here, all compared parts are equal
	if len(pre1) != len(pre2) {
		if len(pre1) > len(pre2) {
			return 1
		}
		return -1
	}

	return 0
}

func Rcompare(v1, v2 string, options *Options) int {
	return -Compare(v1, v2, options)
}

func Gt(v1, v2 string, options *Options) bool {
	return Compare(v1, v2, options) > 0
}

func Gte(v1, v2 string, options *Options) bool {
	return Compare(v1, v2, options) >= 0
}

func Lt(v1, v2 string, options *Options) bool {
	return Compare(v1, v2, options) < 0
}

func Lte(v1, v2 string, options *Options) bool {
	return Compare(v1, v2, options) <= 0
}

func Eq(v1, v2 string, options *Options) bool {
	return Compare(v1, v2, options) == 0
}

func Neq(v1, v2 string, options *Options) bool {
	return Compare(v1, v2, options) != 0
}

func Cmp(v1 string, operator Operator, v2 string, options *Options) bool {
	switch operator {
	case OpEqual, OpEmpty:
		return Eq(v1, v2, options)
	case OpNotEqual:
		return Neq(v1, v2, options)
	case OpGreater:
		return Gt(v1, v2, options)
	case OpGreaterEqual:
		return Gte(v1, v2, options)
	case OpLess:
		return Lt(v1, v2, options)
	case OpLessEqual:
		return Lte(v1, v2, options)
	case OpStrictEqual:
		return v1 == v2
	case OpStrictNotEqual:
		return v1 != v2
	default:
		return false
	}
}

func Sort(versions []string, options *Options) []string {
	result := make([]string, len(versions))
	copy(result, versions)

	sort.Slice(result, func(i, j int) bool {
		return Compare(result[i], result[j], options) < 0
	})

	return result
}

func Rsort(versions []string, options *Options) []string {
	result := make([]string, len(versions))
	copy(result, versions)

	sort.Slice(result, func(i, j int) bool {
		return Compare(result[i], result[j], options) > 0
	})

	return result
}

func Diff(v1, v2 string, options *Options) *ReleaseType {
	parsed1 := Parse(v1, options)
	parsed2 := Parse(v2, options)

	if parsed1 == nil || parsed2 == nil {
		return nil
	}

	if parsed1.Major != parsed2.Major {
		if len(parsed1.Prerelease) > 0 {
			premajor := ReleasePreMajor
			return &premajor
		}
		major := ReleaseMajor
		return &major
	}

	if parsed1.Minor != parsed2.Minor {
		if len(parsed1.Prerelease) > 0 {
			preminor := ReleasePreMinor
			return &preminor
		}
		minor := ReleaseMinor
		return &minor
	}

	if parsed1.Patch != parsed2.Patch {
		if len(parsed1.Prerelease) > 0 {
			prepatch := ReleasePrePatch
			return &prepatch
		}
		patch := ReleasePatch
		return &patch
	}

	if len(parsed1.Prerelease) > 0 || len(parsed2.Prerelease) > 0 {
		prerelease := ReleasePreRelease
		return &prerelease
	}

	return nil
}

func Inc(version string, release ReleaseType, options *Options, identifier string) string {
	parsed := Parse(version, options)
	if parsed == nil {
		return ""
	}

	switch release {
	case ReleaseMajor:
		parsed.Major++
		parsed.Minor = 0
		parsed.Patch = 0
		parsed.Prerelease = nil
	case ReleaseMinor:
		parsed.Minor++
		parsed.Patch = 0
		parsed.Prerelease = nil
	case ReleasePatch:
		parsed.Patch++
		parsed.Prerelease = nil
	case ReleasePreMajor:
		parsed.Major++
		parsed.Minor = 0
		parsed.Patch = 0
		parsed.Prerelease = []string{"0"}
		if identifier != "" {
			parsed.Prerelease = []string{identifier, "0"}
		}
	case ReleasePreMinor:
		parsed.Minor++
		parsed.Patch = 0
		parsed.Prerelease = []string{"0"}
		if identifier != "" {
			parsed.Prerelease = []string{identifier, "0"}
		}
	case ReleasePrePatch:
		parsed.Patch++
		parsed.Prerelease = []string{"0"}
		if identifier != "" {
			parsed.Prerelease = []string{identifier, "0"}
		}
	case ReleasePreRelease:
		if len(parsed.Prerelease) == 0 {
			parsed.Prerelease = []string{"0"}
		} else {
			// Increment the last numeric part
			last := len(parsed.Prerelease) - 1
			if num, err := strconv.Atoi(parsed.Prerelease[last]); err == nil {
				parsed.Prerelease[last] = strconv.Itoa(num + 1)
			} else {
				parsed.Prerelease = append(parsed.Prerelease, "0")
			}
		}
	}

	// Rebuild version string
	versionStr := fmt.Sprintf("%d.%d.%d", parsed.Major, parsed.Minor, parsed.Patch)
	if len(parsed.Prerelease) > 0 {
		versionStr += "-" + strings.Join(parsed.Prerelease, ".")
	}
	if len(parsed.Build) > 0 {
		versionStr += "+" + strings.Join(parsed.Build, ".")
	}

	parsed.Version = versionStr
	return versionStr
}

func Coerce(version string, options *CoerceOptions) *SemVer {
	if version == "" {
		return nil
	}

	var opts *Options
	if options != nil {
		opts = &options.Options
	}

	// Try to parse as-is first
	parsed := Parse(version, opts)
	if parsed != nil {
		return parsed
	}

	// Try to extract numbers from the string
	numbers := regexp.MustCompile(`\d+`).FindAllString(version, -1)
	if len(numbers) == 0 {
		return nil
	}

	// Build a version string from the numbers
	versionStr := numbers[0]
	if len(numbers) > 1 {
		versionStr += "." + numbers[1]
	} else {
		versionStr += ".0"
	}
	if len(numbers) > 2 {
		versionStr += "." + numbers[2]
	} else {
		versionStr += ".0"
	}

	return Parse(versionStr, opts)
}
