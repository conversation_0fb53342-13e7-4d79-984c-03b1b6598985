/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import "strings"

// Severity represents severity levels
type Severity int

const (
	// SeverityIgnore represents ignored severity
	SeverityIgnore Severity = iota
	// SeverityInfo represents informational severity
	SeverityInfo
	// SeverityWarning represents warning severity
	SeverityWarning
	// SeverityError represents error severity
	SeverityError
)

// String returns the string representation of the severity
func (s Severity) String() string {
	switch s {
	case SeverityIgnore:
		return "ignore"
	case SeverityInfo:
		return "info"
	case SeverityWarning:
		return "warning"
	case SeverityError:
		return "error"
	default:
		return "unknown"
	}
}

// FromString creates a Severity from a string
func SeverityFromString(str string) Severity {
	switch strings.ToLower(str) {
	case "ignore":
		return SeverityIgnore
	case "info":
		return SeverityInfo
	case "warning", "warn":
		return SeverityWarning
	case "error", "err":
		return SeverityError
	default:
		return SeverityInfo
	}
}

// Compare compares two severities
// Returns -1 if s < other, 0 if s == other, 1 if s > other
func (s Severity) Compare(other Severity) int {
	if s < other {
		return -1
	} else if s > other {
		return 1
	}
	return 0
}

// Max returns the maximum of two severities
func MaxSeverity(a, b Severity) Severity {
	if a > b {
		return a
	}
	return b
}

// Min returns the minimum of two severities
func MinSeverity(a, b Severity) Severity {
	if a < b {
		return a
	}
	return b
}

// ToNumber converts severity to a number for compatibility
func (s Severity) ToNumber() int {
	return int(s)
}

// FromNumber creates a Severity from a number
func SeverityFromNumber(num int) Severity {
	switch num {
	case 0:
		return SeverityIgnore
	case 1:
		return SeverityInfo
	case 2:
		return SeverityWarning
	case 3:
		return SeverityError
	default:
		return SeverityInfo
	}
}
