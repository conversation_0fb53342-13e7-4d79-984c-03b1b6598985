/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"strings"
)

// TernarySearchTree is a generic ternary search tree implementation
type TernarySearchTree[E any] struct {
	root *tstNode[E]
}

// tstNode represents a node in the ternary search tree
type tstNode[E any] struct {
	character rune
	value     *E
	left      *tstNode[E]
	mid       *tstNode[E]
	right     *tstNode[E]
}

// ForStrings creates a ternary search tree for string keys.
func ForStrings[E any]() *TernarySearchTree[E] {
	return &TernarySearchTree[E]{}
}

// ForUris creates a ternary search tree for URI keys.
func ForUris[E any](ignorePathCasing func() bool) *TernarySearchTree[E] {
	// For now, we'll use the string representation of URIs as keys
	// In a more complete implementation, we might want to handle case sensitivity
	return &TernarySearchTree[E]{}
}

// Set adds or updates an entry in the tree
func (tst *TernarySearchTree[E]) Set(key string, element E) *E {
	if key == "" {
		return nil
	}
	var node *tstNode[E]
	runes := []rune(key)
	if tst.root == nil {
		tst.root = &tstNode[E]{character: runes[0]}
	}

	node = tst.root
	i := 0
	for {
		char := runes[i]
		if char < node.character {
			if node.left == nil {
				node.left = &tstNode[E]{character: char}
			}
			node = node.left
		} else if char > node.character {
			if node.right == nil {
				node.right = &tstNode[E]{character: char}
			}
			node = node.right
		} else {
			i++
			if i == len(runes) {
				oldValue := node.value
				node.value = &element
				return oldValue
			}
			if node.mid == nil {
				node.mid = &tstNode[E]{character: runes[i]}
			}
			node = node.mid
		}
	}
}

// Get retrieves a value from the tree
func (tst *TernarySearchTree[E]) Get(key string) *E {
	if key == "" {
		return nil
	}
	node := tst.root
	runes := []rune(key)
	i := 0
	for node != nil {
		char := runes[i]
		if char < node.character {
			node = node.left
		} else if char > node.character {
			node = node.right
		} else {
			i++
			if i == len(runes) {
				return node.value
			}
			node = node.mid
		}
	}
	return nil
}

// Delete removes an element from the tree
func (tst *TernarySearchTree[E]) Delete(key string) bool {
	if key == "" {
		return false
	}
	// For simplicity, we'll just set the value to nil
	// A full implementation would remove the node if it has no children
	node := tst.root
	runes := []rune(key)
	i := 0
	for node != nil && i < len(runes) {
		char := runes[i]
		if char < node.character {
			node = node.left
		} else if char > node.character {
			node = node.right
		} else {
			if i == len(runes)-1 {
				if node.value != nil {
					node.value = nil
					return true
				}
				return false
			}
			node = node.mid
			i++
		}
	}
	return false
}

// FindSubstr finds all entries whose keys contain the given substring.
// Note: This is a simplified implementation. A more efficient version would require a different tree structure.
func (tst *TernarySearchTree[E]) FindSubstr(key string) []*E {
	if key == "" {
		return nil
	}
	var result []*E
	tst.forEach(func(k string, v E) {
		if strings.Contains(k, key) {
			result = append(result, &v)
		}
	})
	return result
}

// ForEach iterates over all entries in the tree
func (tst *TernarySearchTree[E]) ForEach(callback func(key string, value E)) {
	tst.forEach(callback)
}

func (tst *TernarySearchTree[E]) forEach(callback func(key string, value E)) {
	var walk func(*tstNode[E], string)
	walk = func(node *tstNode[E], key string) {
		if node == nil {
			return
		}

		// Left
		walk(node.left, key)

		// Middle
		newKey := key + string(node.character)
		if node.value != nil {
			callback(newKey, *node.value)
		}
		walk(node.mid, newKey)

		// Right
		walk(node.right, key)
	}
	walk(tst.root, "")
}
