/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"encoding/json"
	"reflect"
)

// IURITransformer provides URI transformation capabilities for IPC
type IURITransformer interface {
	// TransformIncoming transforms incoming URIs
	TransformIncoming(uri *URI) *URI
	// TransformOutgoing transforms outgoing URIs
	TransformOutgoing(uri *URI) *URI
}

// DefaultURITransformer provides a default implementation that doesn't transform
type DefaultURITransformer struct{}

// TransformIncoming implements IURITransformer
func (d *DefaultURITransformer) TransformIncoming(uri *URI) *URI {
	return uri
}

// TransformOutgoing implements IURITransformer
func (d *DefaultURITransformer) TransformOutgoing(uri *URI) *URI {
	return uri
}

// TransformOutgoingURIs transforms all URIs in a data structure using the provided transformer
func TransformOutgoingURIs(data interface{}, transformer IURITransformer) interface{} {
	if data == nil || transformer == nil {
		return data
	}

	return transformURIsRecursive(data, transformer.TransformOutgoing)
}

// TransformIncomingURIs transforms all URIs in a data structure using the provided transformer
func TransformIncomingURIs(data interface{}, transformer IURITransformer) interface{} {
	if data == nil || transformer == nil {
		return data
	}

	return transformURIsRecursive(data, transformer.TransformIncoming)
}

// transformURIsRecursive recursively transforms URIs in a data structure
func transformURIsRecursive(data interface{}, transform func(*URI) *URI) interface{} {
	if data == nil {
		return nil
	}

	value := reflect.ValueOf(data)
	switch value.Kind() {
	case reflect.Ptr:
		if value.IsNil() {
			return nil
		}
		// Check if it's a URI pointer
		if uri, ok := data.(*URI); ok {
			return transform(uri)
		}
		// Recursively transform the pointed-to value
		transformed := transformURIsRecursive(value.Elem().Interface(), transform)
		if transformed == nil {
			return nil
		}
		// Create a new pointer to the transformed value
		newPtr := reflect.New(reflect.TypeOf(transformed))
		newPtr.Elem().Set(reflect.ValueOf(transformed))
		return newPtr.Interface()

	case reflect.Slice:
		if value.IsNil() {
			return nil
		}
		newSlice := reflect.MakeSlice(value.Type(), value.Len(), value.Cap())
		for i := 0; i < value.Len(); i++ {
			transformed := transformURIsRecursive(value.Index(i).Interface(), transform)
			if transformed != nil {
				newSlice.Index(i).Set(reflect.ValueOf(transformed))
			}
		}
		return newSlice.Interface()

	case reflect.Map:
		if value.IsNil() {
			return nil
		}
		newMap := reflect.MakeMap(value.Type())
		for _, key := range value.MapKeys() {
			originalValue := value.MapIndex(key)
			transformedValue := transformURIsRecursive(originalValue.Interface(), transform)
			if transformedValue != nil {
				newMap.SetMapIndex(key, reflect.ValueOf(transformedValue))
			}
		}
		return newMap.Interface()

	case reflect.Struct:
		newStruct := reflect.New(value.Type()).Elem()
		for i := 0; i < value.NumField(); i++ {
			field := value.Field(i)
			if field.CanInterface() {
				transformed := transformURIsRecursive(field.Interface(), transform)
				if transformed != nil && newStruct.Field(i).CanSet() {
					newStruct.Field(i).Set(reflect.ValueOf(transformed))
				}
			}
		}
		return newStruct.Interface()

	default:
		return data
	}
}

// SerializeURIs serializes URIs to JSON-friendly format
func SerializeURIs(data interface{}) interface{} {
	return transformURIsRecursive(data, func(uri *URI) *URI {
		// Convert URI to its JSON representation
		return uri // In a real implementation, this might convert to UriComponents
	})
}

// DeserializeURIs deserializes URIs from JSON-friendly format
func DeserializeURIs(data interface{}) interface{} {
	return transformURIsRecursive(data, func(uri *URI) *URI {
		// Convert from JSON representation back to URI
		return uri // In a real implementation, this might convert from UriComponents
	})
}

// URIReviver can be used with JSON unmarshaling to revive URIs
type URIReviver struct{}

// Revive implements JSON reviving for URIs
func (ur *URIReviver) Revive(key string, value interface{}) interface{} {
	if key == "" {
		return value
	}

	// Check if this looks like a URI object
	if obj, ok := value.(map[string]interface{}); ok {
		if scheme, hasScheme := obj["scheme"]; hasScheme {
			if schemeStr, ok := scheme.(string); ok {
				// Try to construct a URI from the object
				components := UriComponents{
					Scheme: schemeStr,
				}
				if authority, ok := obj["authority"].(string); ok {
					components.Authority = authority
				}
				if path, ok := obj["path"].(string); ok {
					components.Path = path
				}
				if query, ok := obj["query"].(string); ok {
					components.Query = query
				}
				if fragment, ok := obj["fragment"].(string); ok {
					components.Fragment = fragment
				}
				return From(components)
			}
		}
	}

	return value
}

// MarshalURIJSON marshals a URI to JSON
func MarshalURIJSON(uri *URI) ([]byte, error) {
	if uri == nil {
		return json.Marshal(nil)
	}
	return json.Marshal(uri.ToJSON())
}

// UnmarshalURIJSON unmarshals a URI from JSON
func UnmarshalURIJSON(data []byte) (*URI, error) {
	var components UriComponents
	if err := json.Unmarshal(data, &components); err != nil {
		return nil, err
	}
	return From(components), nil
}
