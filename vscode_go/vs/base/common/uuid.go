/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"github.com/google/uuid"
)

// IsUUID checks if the given string is a valid UUID
func IsUUID(value string) bool {
	_, err := uuid.Parse(value)
	return err == nil
}

// GenerateUuid generates a new UUID v4
func GenerateUuid() string {
	return uuid.New().String()
}
