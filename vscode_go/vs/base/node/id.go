/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"crypto/sha256"
	"encoding/hex"
	"net"
	"strings"
	"sync"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
)

var (
	virtualMachineOUIs *basecommon.TernarySearchTree[bool]
	vmOnce             sync.Once
)

func isVirtualMachineMacAddress(mac string) bool {
	vmOnce.Do(func() {
		virtualMachineOUIs = basecommon.ForStrings[bool]()
		// dash-separated
		virtualMachineOUIs.Set("00-50-56", true)
		virtualMachineOUIs.Set("00-0C-29", true)
		virtualMachineOUIs.Set("00-05-69", true)
		virtualMachineOUIs.Set("00-03-FF", true)
		virtualMachineOUIs.Set("00-1C-42", true)
		virtualMachineOUIs.Set("00-16-3E", true)
		virtualMachineOUIs.Set("08-00-27", true)

		// colon-separated
		virtualMachineOUIs.Set("00:50:56", true)
		virtualMachineOUIs.Set("00:0C:29", true)
		virtualMachineOUIs.Set("00:05:69", true)
		virtualMachineOUIs.Set("00:03:FF", true)
		virtualMachineOUIs.Set("00:1C:42", true)
		virtualMachineOUIs.Set("00:16:3E", true)
		virtualMachineOUIs.Set("08:00:27", true)
	})
	return virtualMachineOUIs.FindSubstr(mac) != nil
}

var (
	virtualMachineHint float64
	hintOnce           sync.Once
)

func VirtualMachineHint() float64 {
	hintOnce.Do(func() {
		var vmOui, interfaceCount int
		interfaces, err := net.Interfaces()
		if err != nil {
			virtualMachineHint = 0
			return
		}

		for _, iface := range interfaces {
			if (iface.Flags & net.FlagLoopback) == 0 {
				interfaceCount++
				if isVirtualMachineMacAddress(strings.ToUpper(iface.HardwareAddr.String())) {
					vmOui++
				}
			}
		}

		if interfaceCount > 0 {
			virtualMachineHint = float64(vmOui) / float64(interfaceCount)
		} else {
			virtualMachineHint = 0
		}
	})
	return virtualMachineHint
}

var (
	machineID     string
	machineIDOnce sync.Once
)

func GetMachineId(errorLogger func(error)) (string, error) {
	machineIDOnce.Do(func() {
		id, err := getMacMachineID(errorLogger)
		if err != nil || id == "" {
			machineID = basecommon.GenerateUuid()
		} else {
			machineID = id
		}
	})
	return machineID, nil
}

func getMacMachineID(errorLogger func(error)) (string, error) {
	macAddress, err := GetMac()
	if err != nil {
		errorLogger(err)
		return "", err
	}
	hash := sha256.Sum256([]byte(macAddress))
	return hex.EncodeToString(hash[:]), nil
}
