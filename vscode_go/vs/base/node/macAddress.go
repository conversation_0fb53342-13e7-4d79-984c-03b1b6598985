/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"errors"
	"net"
	"strings"
)

var invalidMacAddresses = map[string]struct{}{
	"00:00:00:00:00:00": {},
	"ff:ff:ff:ff:ff:ff": {},
	"ac:de:48:00:11:22": {},
}

func validateMacAddress(candidate string) bool {
	tempCandidate := strings.ReplaceAll(candidate, "-", ":")
	tempCandidate = strings.ToLower(tempCandidate)
	_, found := invalidMacAddresses[tempCandidate]
	return !found
}

// GetMac returns the first valid MAC address found on the system.
func GetMac() (string, error) {
	ifaces, err := net.Interfaces()
	if err != nil {
		return "", err
	}

	for _, iface := range ifaces {
		mac := iface.HardwareAddr.String()
		if mac != "" && validateMacAddress(mac) {
			return mac, nil
		}
	}

	return "", errors.New("unable to retrieve mac address (unexpected format)")
}
