/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"runtime"
	"strconv"
	"strings"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
)

// ListProcesses returns a process tree starting from the given root PID
func ListProcesses(rootPid int) (*basecommon.ProcessItem, error) {
	var rootItem *basecommon.ProcessItem
	processMap := make(map[int]*basecommon.ProcessItem)
	totalMemory := getTotalMemory()

	addToTree := func(pid, ppid int, cmd string, load, mem float64) {
		parent := processMap[ppid]
		if pid == rootPid || parent != nil {
			item := &basecommon.ProcessItem{
				Name: findName(cmd),
				Cmd:  cmd,
				PID:  pid,
				PPID: ppid,
				Load: load,
				Mem:  adjustMemory(mem, totalMemory),
			}
			processMap[pid] = item

			if pid == rootPid {
				rootItem = item
			}

			if parent != nil {
				if parent.Children == nil {
					parent.Children = make([]*basecommon.ProcessItem, 0)
				}
				parent.Children = append(parent.Children, item)
				// Sort children by PID
				if len(parent.Children) > 1 {
					sortProcessItems(parent.Children)
				}
			}
		}
	}

	if runtime.GOOS == "windows" {
		return listProcessesWindows(rootPid, addToTree)
	}

	// Unix-like systems (macOS, Linux)
	return listProcessesUnix(rootPid, addToTree, rootItem, processMap)
}

// findName extracts a meaningful name from the command line
func findName(cmd string) string {
	// Windows crash reporter
	windowsCrashReporter := regexp.MustCompile(`--crashes-directory`)
	if windowsCrashReporter.MatchString(cmd) {
		return "electron-crash-reporter"
	}

	// Winpty process
	winpty := regexp.MustCompile(`\\pipe\\winpty-control`)
	if winpty.MatchString(cmd) {
		return "winpty-agent"
	}

	// Conpty process
	conpty := regexp.MustCompile(`conhost\.exe.+--headless`)
	if conpty.MatchString(cmd) {
		return "conpty-agent"
	}

	// Find "--type=xxxx"
	typeRegex := regexp.MustCompile(`--type=([a-zA-Z-]+)`)
	matches := typeRegex.FindStringSubmatch(cmd)
	if len(matches) == 2 {
		switch matches[1] {
		case "renderer":
			return "window"
		case "utility":
			utilityNetworkHint := regexp.MustCompile(`--utility-sub-type=network`)
			if utilityNetworkHint.MatchString(cmd) {
				return "utility-network-service"
			}
			return "utility-process"
		case "extensionHost":
			return "extension-host" // normalize remote extension host type
		default:
			return matches[1]
		}
	}

	// Find all xxxx.js
	jsRegex := regexp.MustCompile(`[a-zA-Z-]+\.js`)
	jsMatches := jsRegex.FindAllString(cmd, -1)
	if len(jsMatches) > 0 {
		result := strings.Join(jsMatches, " ")
		if !strings.Contains(cmd, "node ") && !strings.Contains(cmd, "node.exe") {
			return fmt.Sprintf("electron-nodejs (%s)", result)
		}
	}

	return cmd
}

// adjustMemory adjusts memory value based on platform
func adjustMemory(mem, totalMemory float64) float64 {
	if runtime.GOOS == "windows" {
		return mem
	}
	// On Unix systems, convert percentage to bytes
	return totalMemory * (mem / 100)
}

// getTotalMemory returns total system memory in bytes
func getTotalMemory() float64 {
	if runtime.GOOS == "windows" {
		// On Windows, we'll use a simplified approach
		return 8 * 1024 * 1024 * 1024 // 8GB default
	}

	// On Unix systems, try to get actual memory
	if data, err := os.ReadFile("/proc/meminfo"); err == nil {
		lines := strings.Split(string(data), "\n")
		for _, line := range lines {
			if strings.HasPrefix(line, "MemTotal:") {
				fields := strings.Fields(line)
				if len(fields) >= 2 {
					if kb, err := strconv.ParseFloat(fields[1], 64); err == nil {
						return kb * 1024 // Convert KB to bytes
					}
				}
				break
			}
		}
	}

	// Fallback
	return 8 * 1024 * 1024 * 1024 // 8GB default
}

// listProcessesWindows handles Windows process listing
func listProcessesWindows(rootPid int, addToTree func(int, int, string, float64, float64)) (*basecommon.ProcessItem, error) {
	// For Windows, we would use a Windows-specific process tree library
	// Since we're in Go, we'll use a simplified approach with tasklist
	cmd := exec.Command("tasklist", "/fo", "csv", "/v")
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("failed to execute tasklist: %v", err)
	}

	return parseTasklistOutput(string(output), rootPid, addToTree)
}

// parseTasklistOutput parses Windows tasklist output
func parseTasklistOutput(output string, rootPid int, addToTree func(int, int, string, float64, float64)) (*basecommon.ProcessItem, error) {
	lines := strings.Split(output, "\n")
	if len(lines) < 2 {
		return nil, fmt.Errorf("invalid tasklist output")
	}

	var rootItem *basecommon.ProcessItem
	processMap := make(map[int]*basecommon.ProcessItem)

	// Skip header line
	for i := 1; i < len(lines); i++ {
		line := strings.TrimSpace(lines[i])
		if line == "" {
			continue
		}

		// Parse CSV line (simplified)
		fields := parseCSVLine(line)
		if len(fields) < 5 {
			continue
		}

		// Extract PID from second field
		pidStr := strings.Trim(fields[1], "\"")
		pid, err := strconv.Atoi(pidStr)
		if err != nil {
			continue
		}

		// For simplicity, assume PPID is 0 (we'd need additional tools to get real PPID on Windows)
		ppid := 0
		cmd := strings.Trim(fields[0], "\"")
		load := 0.0
		mem := 0.0

		// Try to parse memory from field 4 (Memory Usage)
		if len(fields) > 4 {
			memStr := strings.Trim(fields[4], "\"")
			memStr = strings.ReplaceAll(memStr, ",", "")
			memStr = strings.ReplaceAll(memStr, " K", "")
			if memVal, err := strconv.ParseFloat(memStr, 64); err == nil {
				mem = memVal * 1024 // Convert KB to bytes
			}
		}

		addToTree(pid, ppid, cmd, load, mem)

		if pid == rootPid {
			rootItem = processMap[pid]
		}
	}

	if rootItem == nil {
		return nil, fmt.Errorf("root process %d not found", rootPid)
	}

	return rootItem, nil
}

// parseCSVLine parses a CSV line (simplified)
func parseCSVLine(line string) []string {
	var fields []string
	var current strings.Builder
	inQuotes := false

	for _, char := range line {
		switch char {
		case '"':
			inQuotes = !inQuotes
			current.WriteRune(char)
		case ',':
			if inQuotes {
				current.WriteRune(char)
			} else {
				fields = append(fields, current.String())
				current.Reset()
			}
		default:
			current.WriteRune(char)
		}
	}

	if current.Len() > 0 {
		fields = append(fields, current.String())
	}

	return fields
}

// listProcessesUnix handles Unix-like process listing
func listProcessesUnix(rootPid int, addToTree func(int, int, string, float64, float64), rootItem *basecommon.ProcessItem, processMap map[int]*basecommon.ProcessItem) (*basecommon.ProcessItem, error) {
	// Check if ps command exists
	if _, err := exec.LookPath("ps"); err != nil {
		// Try using a shell script approach for Linux
		if runtime.GOOS == "linux" {
			return listProcessesLinuxFallback(rootPid, addToTree)
		}
		return nil, fmt.Errorf("ps command not found: %v", err)
	}

	// Use ps command
	args := []string{"-ax", "-o", "pid=,ppid=,pcpu=,pmem=,command="}
	cmd := exec.Command("ps", args...)
	cmd.Env = append(os.Environ(), "LC_NUMERIC=en_US.UTF-8")
	output, err := cmd.Output()
	if err != nil {
		return nil, fmt.Errorf("failed to execute ps: %v", err)
	}

	parsePsOutput(string(output), addToTree)

	if runtime.GOOS == "linux" {
		// Calculate Linux CPU usage
		if err := calculateLinuxCpuUsage(processMap); err != nil {
			// Continue without CPU usage data
		}
	}

	rootItem = processMap[rootPid]
	if rootItem == nil {
		return nil, fmt.Errorf("root process %d not found", rootPid)
	}

	return rootItem, nil
}

// listProcessesLinuxFallback uses a fallback method for Linux
func listProcessesLinuxFallback(rootPid int, addToTree func(int, int, string, float64, float64)) (*basecommon.ProcessItem, error) {
	// Read from /proc filesystem
	procDir := "/proc"
	entries, err := os.ReadDir(procDir)
	if err != nil {
		return nil, fmt.Errorf("failed to read /proc: %v", err)
	}

	var rootItem *basecommon.ProcessItem
	processMap := make(map[int]*basecommon.ProcessItem)

	for _, entry := range entries {
		if !entry.IsDir() {
			continue
		}

		// Check if directory name is a number (PID)
		pid, err := strconv.Atoi(entry.Name())
		if err != nil {
			continue
		}

		// Read process info
		statPath := filepath.Join(procDir, entry.Name(), "stat")
		statData, err := os.ReadFile(statPath)
		if err != nil {
			continue
		}

		cmdlinePath := filepath.Join(procDir, entry.Name(), "cmdline")
		cmdlineData, err := os.ReadFile(cmdlinePath)
		if err != nil {
			continue
		}

		// Parse stat file
		statFields := strings.Fields(string(statData))
		if len(statFields) < 4 {
			continue
		}

		ppid, err := strconv.Atoi(statFields[3])
		if err != nil {
			continue
		}

		// Parse command line
		cmdline := string(cmdlineData)
		cmdline = strings.ReplaceAll(cmdline, "\x00", " ")
		cmdline = strings.TrimSpace(cmdline)
		if cmdline == "" {
			cmdline = statFields[1] // Use process name from stat
		}

		load := 0.0
		mem := 0.0

		addToTree(pid, ppid, cmdline, load, mem)

		if pid == rootPid {
			rootItem = processMap[pid]
		}
	}

	if rootItem == nil {
		return nil, fmt.Errorf("root process %d not found", rootPid)
	}

	return rootItem, nil
}

// parsePsOutput parses ps command output
func parsePsOutput(output string, addToTree func(int, int, string, float64, float64)) {
	pidCmdRegex := regexp.MustCompile(`^\s*([0-9]+)\s+([0-9]+)\s+([0-9]+\.[0-9]+)\s+([0-9]+\.[0-9]+)\s+(.+)$`)
	lines := strings.Split(output, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		matches := pidCmdRegex.FindStringSubmatch(line)
		if len(matches) == 6 {
			pid, err1 := strconv.Atoi(matches[1])
			ppid, err2 := strconv.Atoi(matches[2])
			load, err3 := strconv.ParseFloat(matches[3], 64)
			mem, err4 := strconv.ParseFloat(matches[4], 64)

			if err1 == nil && err2 == nil && err3 == nil && err4 == nil {
				addToTree(pid, ppid, matches[5], load, mem)
			}
		}
	}
}

// calculateLinuxCpuUsage calculates CPU usage for Linux processes
func calculateLinuxCpuUsage(processMap map[int]*basecommon.ProcessItem) error {
	// Get list of PIDs
	var pids []string
	for pid := range processMap {
		pids = append(pids, strconv.Itoa(pid))
	}

	if len(pids) == 0 {
		return nil
	}

	// Use a simplified CPU usage calculation
	// In a real implementation, this would use the cpuUsage.sh script
	// For now, we'll just leave the CPU usage as reported by ps
	return nil
}

// sortProcessItems sorts process items by PID
func sortProcessItems(items []*basecommon.ProcessItem) {
	for i := 0; i < len(items)-1; i++ {
		for j := i + 1; j < len(items); j++ {
			if items[i].PID > items[j].PID {
				items[i], items[j] = items[j], items[i]
			}
		}
	}
}

// WindowsProcessTreeData represents Windows process tree data
type WindowsProcessTreeData struct {
	PID         int     `json:"pid"`
	PPID        int     `json:"ppid"`
	CommandLine string  `json:"commandLine"`
	CPU         float64 `json:"cpu"`
	Memory      float64 `json:"memory"`
}

// GetProcessList simulates the Windows process tree functionality
func GetProcessList(rootPid int) ([]WindowsProcessTreeData, error) {
	// This would interface with a Windows-specific library
	// For now, return empty slice
	return []WindowsProcessTreeData{}, nil
}

// GetProcessCpuUsage simulates getting CPU usage for Windows processes
func GetProcessCpuUsage(processList []WindowsProcessTreeData) ([]WindowsProcessTreeData, error) {
	// This would calculate CPU usage for Windows processes
	// For now, return the same list
	return processList, nil
}

// CleanUNCPrefix cleans UNC prefixes from Windows paths
func CleanUNCPrefix(value string) string {
	if strings.HasPrefix(value, "\\\\?\\") {
		return value[4:]
	}
	if strings.HasPrefix(value, "\\??\\") {
		return value[4:]
	}
	if strings.HasPrefix(value, "\"\\\\?\\") {
		return "\"" + value[5:]
	}
	if strings.HasPrefix(value, "\"\\??\\") {
		return "\"" + value[5:]
	}
	return value
}
