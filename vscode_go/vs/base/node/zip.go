/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"archive/zip"
	"bytes"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"regexp"
	"strings"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	"github.com/yudaprama/kawai-agent/vscode_go/vs/nls"
)

var CorruptZipMessage = "end of central directory record signature not found"
var CorruptZipPattern = regexp.MustCompile(regexp.QuoteMeta(CorruptZipMessage))

type IExtractOptions struct {
	Overwrite  bool
	SourcePath string
}

type ExtractErrorType string

const (
	CorruptZip ExtractErrorType = "CorruptZip"
	Incomplete ExtractErrorType = "Incomplete"
)

type ExtractError struct {
	Type  ExtractErrorType
	Cause error
}

func (e *ExtractError) Error() string {
	message := e.Cause.Error()
	switch e.Type {
	case CorruptZip:
		message = fmt.Sprintf("Corrupt ZIP: %s", message)
	}
	return message
}

func (e *ExtractError) Unwrap() error {
	return e.Cause
}

func ToExtractError(err error) *ExtractError {
	if extractErr, ok := err.(*ExtractError); ok {
		return extractErr
	}

	var errorType ExtractErrorType
	if CorruptZipPattern.MatchString(err.Error()) {
		errorType = CorruptZip
	}

	return &ExtractError{
		Type:  errorType,
		Cause: err,
	}
}

type IFile struct {
	Path      string
	Contents  []byte
	LocalPath string
}

func Zip(zipPath string, files []IFile) (string, error) {
	file, err := os.Create(zipPath)
	if err != nil {
		return "", err
	}
	defer file.Close()

	zipWriter := zip.NewWriter(file)
	defer zipWriter.Close()

	for _, f := range files {
		writer, err := zipWriter.Create(f.Path)
		if err != nil {
			return "", err
		}

		if f.Contents != nil {
			_, err = writer.Write(f.Contents)
			if err != nil {
				return "", err
			}
		} else if f.LocalPath != "" {
			localFile, err := os.Open(f.LocalPath)
			if err != nil {
				return "", err
			}
			defer localFile.Close()

			_, err = io.Copy(writer, localFile)
			if err != nil {
				return "", err
			}
		}
	}

	return zipPath, nil
}

func Extract(zipPath string, targetPath string, options IExtractOptions, token basecommon.CancellationToken) error {
	var sourcePathRegex *regexp.Regexp
	if options.SourcePath != "" {
		sourcePathRegex = regexp.MustCompile("^" + regexp.QuoteMeta(options.SourcePath))
	}

	if options.Overwrite {
		err := os.RemoveAll(targetPath)
		if err != nil && !os.IsNotExist(err) {
			return err
		}
	}

	reader, err := zip.OpenReader(zipPath)
	if err != nil {
		return ToExtractError(err)
	}
	defer reader.Close()

	extractedCount := 0
	totalEntries := len(reader.File)

	for _, file := range reader.File {
		if token.IsCancellationRequested() {
			return basecommon.CancellationError{}
		}

		fileName := file.Name
		if sourcePathRegex != nil {
			if !sourcePathRegex.MatchString(fileName) {
				continue
			}
			fileName = sourcePathRegex.ReplaceAllString(fileName, "")
		}

		targetFilePath := filepath.Join(targetPath, fileName)

		// Security check: ensure the target path is within the target directory
		if !strings.HasPrefix(targetFilePath, filepath.Clean(targetPath)+string(os.PathSeparator)) {
			return fmt.Errorf(nls.Localize("invalid file", "Error extracting {0}. Invalid file.", fileName))
		}

		if file.FileInfo().IsDir() {
			err := os.MkdirAll(targetFilePath, file.FileInfo().Mode())
			if err != nil {
				return err
			}
		} else {
			err := extractFile(file, targetFilePath, token)
			if err != nil {
				return err
			}
		}

		extractedCount++
	}

	if extractedCount != totalEntries {
		return &ExtractError{
			Type:  Incomplete,
			Cause: fmt.Errorf(nls.Localize("incompleteExtract", "Incomplete. Found %d of %d entries", extractedCount, totalEntries)),
		}
	}

	return nil
}

func extractFile(file *zip.File, targetPath string, token basecommon.CancellationToken) error {
	if token.IsCancellationRequested() {
		return basecommon.CancellationError{}
	}

	err := os.MkdirAll(filepath.Dir(targetPath), 0755)
	if err != nil {
		return err
	}

	reader, err := file.Open()
	if err != nil {
		return ToExtractError(err)
	}
	defer reader.Close()

	writer, err := os.OpenFile(targetPath, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, file.FileInfo().Mode())
	if err != nil {
		return err
	}
	defer writer.Close()

	_, err = io.Copy(writer, reader)
	if err != nil {
		return err
	}

	return nil
}

func Buffer(zipPath string, filePath string) ([]byte, error) {
	reader, err := zip.OpenReader(zipPath)
	if err != nil {
		return nil, ToExtractError(err)
	}
	defer reader.Close()

	for _, file := range reader.File {
		if file.Name == filePath {
			fileReader, err := file.Open()
			if err != nil {
				return nil, ToExtractError(err)
			}
			defer fileReader.Close()

			var buf bytes.Buffer
			_, err = io.Copy(&buf, fileReader)
			if err != nil {
				return nil, err
			}

			return buf.Bytes(), nil
		}
	}

	return nil, fmt.Errorf(nls.Localize("notFound", "%s not found inside zip.", filePath))
}
