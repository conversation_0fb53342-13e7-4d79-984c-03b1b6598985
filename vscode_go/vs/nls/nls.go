/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package nls

import (
	"fmt"
	"strings"
)

// LocalizeInfo represents localization information
type LocalizeInfo struct {
	Key     string   `json:"key"`
	Comment []string `json:"comment,omitempty"`
}

// LocalizeFunc represents a localization function
type LocalizeFunc func(info interface{}, message string, args ...interface{}) string

// defaultLocalizeFunc is the default localization function
var defaultLocalizeFunc LocalizeFunc = func(info interface{}, message string, args ...interface{}) string {
	// Simple implementation that just formats the message with args
	if len(args) > 0 {
		return fmt.Sprintf(message, args...)
	}
	return message
}

// currentLocalizeFunc holds the current localization function
var currentLocalizeFunc = defaultLocalizeFunc

// Localize localizes a message with the given key and arguments
func Localize(info interface{}, message string, args ...interface{}) string {
	return currentLocalizeFunc(info, message, args...)
}

// LocalizeByKey localizes a message by key only
func LocalizeByKey(key string, message string, args ...interface{}) string {
	return Localize(&LocalizeInfo{Key: key}, message, args...)
}

// SetLocalizeFunc sets the localization function
func SetLocalizeFunc(fn LocalizeFunc) {
	if fn != nil {
		currentLocalizeFunc = fn
	} else {
		currentLocalizeFunc = defaultLocalizeFunc
	}
}

// GetLocalizeFunc returns the current localization function
func GetLocalizeFunc() LocalizeFunc {
	return currentLocalizeFunc
}

// Config represents localization configuration
type Config struct {
	Locale                   string            `json:"locale"`
	AvailableLanguages       map[string]string `json:"availableLanguages"`
	PseudoTranslation        bool              `json:"pseudoTranslation"`
	PseudoTranslationOptions *PseudoOptions    `json:"pseudoTranslationOptions,omitempty"`
}

// PseudoOptions represents pseudo-translation options
type PseudoOptions struct {
	StartDelimiter string `json:"startDelimiter"`
	EndDelimiter   string `json:"endDelimiter"`
	Expand         bool   `json:"expand"`
}

// DefaultConfig returns the default localization configuration
func DefaultConfig() *Config {
	return &Config{
		Locale:             "en",
		AvailableLanguages: map[string]string{"en": "English"},
		PseudoTranslation:  false,
	}
}

// LoadFunc represents a function to load localized strings
type LoadFunc func(bundle string) (map[string]string, error)

// MessageBundle represents a bundle of localized messages
type MessageBundle struct {
	messages map[string]string
	locale   string
}

// NewMessageBundle creates a new message bundle
func NewMessageBundle(locale string) *MessageBundle {
	return &MessageBundle{
		messages: make(map[string]string),
		locale:   locale,
	}
}

// AddMessage adds a message to the bundle
func (mb *MessageBundle) AddMessage(key, message string) {
	mb.messages[key] = message
}

// GetMessage gets a message from the bundle
func (mb *MessageBundle) GetMessage(key string) (string, bool) {
	msg, exists := mb.messages[key]
	return msg, exists
}

// GetLocale returns the bundle's locale
func (mb *MessageBundle) GetLocale() string {
	return mb.locale
}

// BundleManager manages message bundles
type BundleManager struct {
	bundles map[string]*MessageBundle
	config  *Config
}

// NewBundleManager creates a new bundle manager
func NewBundleManager(config *Config) *BundleManager {
	if config == nil {
		config = DefaultConfig()
	}
	return &BundleManager{
		bundles: make(map[string]*MessageBundle),
		config:  config,
	}
}

// LoadBundle loads a message bundle
func (bm *BundleManager) LoadBundle(locale string, loadFunc LoadFunc) error {
	messages, err := loadFunc(locale)
	if err != nil {
		return err
	}

	bundle := NewMessageBundle(locale)
	for key, message := range messages {
		bundle.AddMessage(key, message)
	}

	bm.bundles[locale] = bundle
	return nil
}

// GetBundle gets a message bundle by locale
func (bm *BundleManager) GetBundle(locale string) *MessageBundle {
	return bm.bundles[locale]
}

// LocalizeWithBundle localizes a message using a specific bundle
func (bm *BundleManager) LocalizeWithBundle(locale, key, defaultMessage string, args ...interface{}) string {
	bundle := bm.GetBundle(locale)
	if bundle != nil {
		if message, exists := bundle.GetMessage(key); exists {
			if len(args) > 0 {
				return fmt.Sprintf(message, args...)
			}
			return message
		}
	}

	// Fallback to default message
	if len(args) > 0 {
		return fmt.Sprintf(defaultMessage, args...)
	}
	return defaultMessage
}

// ApplyPseudoTranslation applies pseudo-translation to a message
func ApplyPseudoTranslation(message string, options *PseudoOptions) string {
	if options == nil {
		options = &PseudoOptions{
			StartDelimiter: "[",
			EndDelimiter:   "]",
			Expand:         true,
		}
	}

	result := message
	if options.Expand {
		// Simple expansion by repeating some characters
		result = strings.ReplaceAll(result, "a", "ä")
		result = strings.ReplaceAll(result, "e", "ë")
		result = strings.ReplaceAll(result, "i", "ï")
		result = strings.ReplaceAll(result, "o", "ö")
		result = strings.ReplaceAll(result, "u", "ü")
	}

	return options.StartDelimiter + result + options.EndDelimiter
}

// Global bundle manager instance
var globalBundleManager = NewBundleManager(nil)

// SetGlobalConfig sets the global localization configuration
func SetGlobalConfig(config *Config) {
	globalBundleManager.config = config
}

// GetGlobalConfig returns the global localization configuration
func GetGlobalConfig() *Config {
	return globalBundleManager.config
}

// LoadGlobalBundle loads a bundle into the global manager
func LoadGlobalBundle(locale string, loadFunc LoadFunc) error {
	return globalBundleManager.LoadBundle(locale, loadFunc)
}

// LocalizeGlobal localizes using the global bundle manager
func LocalizeGlobal(key, defaultMessage string, args ...interface{}) string {
	locale := globalBundleManager.config.Locale
	return globalBundleManager.LocalizeWithBundle(locale, key, defaultMessage, args...)
}
