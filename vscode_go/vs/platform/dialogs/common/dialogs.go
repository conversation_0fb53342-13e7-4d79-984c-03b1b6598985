/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	productcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/product/common"
)

// DialogType represents the type of dialog
type DialogType string

const (
	DialogTypeInfo     DialogType = "info"
	DialogTypeWarning  DialogType = "warning"
	DialogTypeError    DialogType = "error"
	DialogTypeQuestion DialogType = "question"
)

// ICheckbox represents a checkbox in a dialog
type ICheckbox struct {
	Label   string `json:"label"`
	Checked bool   `json:"checked"`
}

// ICheckboxResult represents the result of a checkbox
type ICheckboxResult struct {
	CheckboxChecked *bool `json:"checkboxChecked,omitempty"`
}

// ICustomDialogOptions represents custom dialog options
type ICustomDialogOptions struct {
	DisableCloseAction bool `json:"disableCloseAction"`
	MarkdownLinks      bool `json:"markdownLinks"`
}

// IBaseDialogOptions represents base dialog options
type IBaseDialogOptions struct {
	Type     *DialogType           `json:"type,omitempty"`
	Title    *string               `json:"title,omitempty"`
	Message  string                `json:"message"`
	Detail   *string               `json:"detail,omitempty"`
	Checkbox *ICheckbox            `json:"checkbox,omitempty"`
	Custom   *ICustomDialogOptions `json:"custom,omitempty"`
}

// IConfirmation represents a confirmation dialog
type IConfirmation struct {
	IBaseDialogOptions
	PrimaryButton *string `json:"primaryButton,omitempty"`
	CancelButton  *string `json:"cancelButton,omitempty"`
}

// IConfirmationResult represents the result of a confirmation dialog
type IConfirmationResult struct {
	ICheckboxResult
	Confirmed bool `json:"confirmed"`
}

// INativeOpenDialogOptions represents native open dialog options
type INativeOpenDialogOptions struct {
	ForceNewWindow     *bool                  `json:"forceNewWindow,omitempty"`
	DefaultPath        *string                `json:"defaultPath,omitempty"`
	TelemetryEventName *string                `json:"telemetryEventName,omitempty"`
	TelemetryExtraData map[string]interface{} `json:"telemetryExtraData,omitempty"`
}

// FileFilter represents a file filter for dialogs
type FileFilter struct {
	Name       string   `json:"name"`
	Extensions []string `json:"extensions"`
}

// MessageBoxOptions represents message box options
type MessageBoxOptions struct {
	Type                *DialogType `json:"type,omitempty"`
	Buttons             []string    `json:"buttons,omitempty"`
	DefaultId           *int        `json:"defaultId,omitempty"`
	Title               *string     `json:"title,omitempty"`
	Message             string      `json:"message"`
	Detail              *string     `json:"detail,omitempty"`
	CheckboxLabel       *string     `json:"checkboxLabel,omitempty"`
	CheckboxChecked     *bool       `json:"checkboxChecked,omitempty"`
	Icon                *string     `json:"icon,omitempty"`
	CancelId            *int        `json:"cancelId,omitempty"`
	NoLink              *bool       `json:"noLink,omitempty"`
	NormalizeAccessKeys *bool       `json:"normalizeAccessKeys,omitempty"`
}

// MessageBoxReturnValue represents the return value of a message box
type MessageBoxReturnValue struct {
	Response        int   `json:"response"`
	CheckboxChecked *bool `json:"checkboxChecked,omitempty"`
}

// OpenDialogOptions represents open dialog options
type OpenDialogOptions struct {
	Title                   *string      `json:"title,omitempty"`
	DefaultPath             *string      `json:"defaultPath,omitempty"`
	ButtonLabel             *string      `json:"buttonLabel,omitempty"`
	Filters                 []FileFilter `json:"filters,omitempty"`
	Properties              []string     `json:"properties,omitempty"`
	Message                 *string      `json:"message,omitempty"`
	SecurityScopedBookmarks *bool        `json:"securityScopedBookmarks,omitempty"`
}

// OpenDialogReturnValue represents the return value of an open dialog
type OpenDialogReturnValue struct {
	Canceled  bool     `json:"canceled"`
	FilePaths []string `json:"filePaths"`
	Bookmarks []string `json:"bookmarks,omitempty"`
}

// SaveDialogOptions represents save dialog options
type SaveDialogOptions struct {
	Title                   *string      `json:"title,omitempty"`
	DefaultPath             *string      `json:"defaultPath,omitempty"`
	ButtonLabel             *string      `json:"buttonLabel,omitempty"`
	Filters                 []FileFilter `json:"filters,omitempty"`
	Message                 *string      `json:"message,omitempty"`
	NameFieldLabel          *string      `json:"nameFieldLabel,omitempty"`
	ShowsTagField           *bool        `json:"showsTagField,omitempty"`
	SecurityScopedBookmarks *bool        `json:"securityScopedBookmarks,omitempty"`
}

// SaveDialogReturnValue represents the return value of a save dialog
type SaveDialogReturnValue struct {
	Canceled bool    `json:"canceled"`
	FilePath *string `json:"filePath,omitempty"`
	Bookmark *string `json:"bookmark,omitempty"`
}

// IMassagedMessageBoxOptions represents massaged message box options
type IMassagedMessageBoxOptions struct {
	Options       MessageBoxOptions `json:"options"`
	ButtonIndices []int             `json:"buttonIndices"`
}

// MassageMessageBoxOptions massages message box options for platform consistency
func MassageMessageBoxOptions(options MessageBoxOptions, productService productcommon.IProductService) IMassagedMessageBoxOptions {
	// Deep clone the options
	massagedOptions := options

	// Process buttons with mnemonics
	buttons := make([]string, 0)
	if massagedOptions.Buttons != nil {
		for _, button := range massagedOptions.Buttons {
			// Apply mnemonic processing (simplified for now)
			buttons = append(buttons, button)
		}
	}

	// Create button indices mapping
	buttonIndices := make([]int, len(buttons))
	for i := range buttonIndices {
		buttonIndices[i] = i
	}

	// Set defaults
	defaultId := 0
	cancelId := len(buttons) - 1
	if cancelId < 0 {
		cancelId = 0
	}

	massagedOptions.Buttons = buttons
	massagedOptions.DefaultId = &defaultId
	massagedOptions.CancelId = &cancelId

	return IMassagedMessageBoxOptions{
		Options:       massagedOptions,
		ButtonIndices: buttonIndices,
	}
}

// Workspace filter constants
var WORKSPACE_EXTENSION = "code-workspace"
var WORKSPACE_SUFFIX = "." + WORKSPACE_EXTENSION
var WORKSPACE_FILTER = []FileFilter{
	{
		Name:       "Code Workspace", // This would be localized in a real implementation
		Extensions: []string{WORKSPACE_EXTENSION},
	},
}

// BrowserWindow represents a browser window (placeholder for actual implementation)
type BrowserWindow struct {
	ID int `json:"id"`
}

// GetFocusedWindow returns the currently focused window (placeholder)
func GetFocusedWindow() *BrowserWindow {
	// This would be implemented with actual window management
	return nil
}

// Service identifiers for dependency injection
type DialogMainServiceIdentifier struct{}

func (d *DialogMainServiceIdentifier) GetServiceID() string {
	return "dialogMainService"
}

// Global service identifier instance
var IDialogMainServiceID = &DialogMainServiceIdentifier{}

// Localization helpers (simplified implementations)
func Localize(key string, defaultValue string) string {
	// In a real implementation, this would use the localization system
	return defaultValue
}

// Platform-specific constants for dialog properties
var (
	// Dialog properties
	PropertyMultiSelections         = "multiSelections"
	PropertyOpenDirectory           = "openDirectory"
	PropertyOpenFile                = "openFile"
	PropertyCreateDirectory         = "createDirectory"
	PropertyTreatPackageAsDirectory = "treatPackageAsDirectory"
)

// Helper functions for mnemonic processing
func ProcessMnemonicButtonLabel(label string) MnemonicButtonResult {
	// Simplified implementation - in reality this would handle platform-specific mnemonics
	return MnemonicButtonResult{
		WithMnemonic:    label,
		WithoutMnemonic: label,
	}
}

type MnemonicButtonResult struct {
	WithMnemonic    string
	WithoutMnemonic string
}

// NormalizeNFC normalizes a string to NFC form (simplified implementation)
func NormalizeNFC(input string) string {
	// In a real implementation, this would use Unicode normalization
	return input
}

// IDialogService represents the dialog service interface
type IDialogService interface {
	// ServiceBrand for type safety
	ServiceBrand() interface{}

	// Confirm shows a confirmation dialog
	Confirm(options *IConfirmation) (*IConfirmationResult, error)

	// Prompt shows a prompt dialog
	Prompt(options *IPromptOptions) (*IPromptResult, error)

	// About shows an about dialog
	About() error

	// Input shows an input dialog
	Input(options *IInputOptions) (*IInputResult, error)
}

// IPromptOptions represents prompt dialog options
type IPromptOptions struct {
	Type         DialogType       `json:"type"`
	Title        string           `json:"title"`
	Message      string           `json:"message"`
	Detail       string           `json:"detail,omitempty"`
	Buttons      []*IPromptButton `json:"buttons"`
	CancelButton *IPromptButton   `json:"cancelButton,omitempty"`
	Checkbox     *ICheckbox       `json:"checkbox,omitempty"`
	Modal        bool             `json:"modal,omitempty"`
}

// IPromptButton represents a prompt button
type IPromptButton struct {
	Label       string       `json:"label"`
	Run         func() error `json:"-"`
	IsSecondary bool         `json:"isSecondary,omitempty"`
}

// IPromptResult represents the result of a prompt dialog
type IPromptResult struct {
	Result          *IPromptButton `json:"result"`
	CheckboxChecked *bool          `json:"checkboxChecked,omitempty"`
}

// IInputOptions represents input dialog options
type IInputOptions struct {
	Title       string     `json:"title"`
	Message     string     `json:"message"`
	Value       string     `json:"value,omitempty"`
	Placeholder string     `json:"placeholder,omitempty"`
	Password    bool       `json:"password,omitempty"`
	Checkbox    *ICheckbox `json:"checkbox,omitempty"`
}

// IInputResult represents the result of an input dialog
type IInputResult struct {
	Value           string `json:"value"`
	CheckboxChecked *bool  `json:"checkboxChecked,omitempty"`
}

// TestDialogService provides a test implementation of IDialogService
type TestDialogService struct{}

func NewTestDialogService() *TestDialogService {
	return &TestDialogService{}
}

func (s *TestDialogService) ServiceBrand() interface{} {
	return "dialogService"
}

func (s *TestDialogService) Confirm(options *IConfirmation) (*IConfirmationResult, error) {
	return &IConfirmationResult{Confirmed: true}, nil
}

func (s *TestDialogService) Prompt(options *IPromptOptions) (*IPromptResult, error) {
	if len(options.Buttons) > 0 {
		return &IPromptResult{Result: options.Buttons[0]}, nil
	}
	return &IPromptResult{}, nil
}

func (s *TestDialogService) About() error {
	return nil
}

func (s *TestDialogService) Input(options *IInputOptions) (*IInputResult, error) {
	return &IInputResult{Value: options.Value}, nil
}
