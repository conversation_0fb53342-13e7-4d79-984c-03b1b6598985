/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	baseCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	platformInstantiation "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/instantiation/common"
)

// IDownloadService provides download capabilities
var IDownloadService = platformInstantiation.CreateDecorator[IDownloadServiceInterface]("downloadService")

// IDownloadServiceInterface defines the download service interface
type IDownloadServiceInterface interface {
	platformInstantiation.BrandedService

	// Download downloads a resource from URI to target URI
	Download(uri *baseCommon.URI, to *baseCommon.URI, cancellationToken baseCommon.CancellationToken) error
}
