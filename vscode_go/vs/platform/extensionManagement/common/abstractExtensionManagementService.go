/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"fmt"
	"sync"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	"github.com/yudaprama/kawai-agent/vscode_go/vs/nls"
	extensionscommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/extensions/common"
	logcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
	productcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/product/common"
	telemetrycommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/telemetry/common"
	uriidentitycommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/uriIdentity/common"
	userdataprofilecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/userDataProfile/common"
)

// Task options and interfaces
type InstallExtensionTaskOptions struct {
	InstallOptions
	ProfileLocation basecommon.URI    `json:"profileLocation"`
	ProductVersion  IProductVersion   `json:"productVersion"`
	Context         map[string]string `json:"context,omitempty"`
}

type IInstallExtensionTask interface {
	GetManifest() *extensionscommon.IExtensionManifest
	GetIdentifier() extensionscommon.IExtensionIdentifier
	GetSource() interface{} // IGalleryExtension | basecommon.URI
	GetOperation() InstallOperation
	GetOptions() InstallExtensionTaskOptions
	GetVerificationStatus() *ExtensionSignatureVerificationCode
	Run() (*ILocalExtension, error)
	WaitUntilTaskIsFinished() (*ILocalExtension, error)
	Cancel()
}

type UninstallExtensionTaskOptions struct {
	UninstallOptions
	ProfileLocation basecommon.URI `json:"profileLocation"`
}

type IUninstallExtensionTask interface {
	GetOptions() UninstallExtensionTaskOptions
	GetExtension() *ILocalExtension
	Run() error
	WaitUntilTaskIsFinished() error
	Cancel()
}

// Installable extension type
type InstallableExtension struct {
	Manifest  *extensionscommon.IExtensionManifest `json:"manifest"`
	Extension interface{}                          `json:"extension"` // IGalleryExtension | basecommon.URI
	Options   InstallOptions                       `json:"options"`
}

// Extension management participant interface
type IExtensionManagementParticipant interface {
	PostInstall(manifest *extensionscommon.IExtensionManifest, local *ILocalExtension, context map[string]string, token basecommon.CancellationToken) error
	PostUninstall(local *ILocalExtension, context map[string]string, token basecommon.CancellationToken) error
}

// Common extension management service interface
type IExtensionManagementService interface {
	// Service brand
	ServiceBrand() interface{}

	// Properties
	GetPreferPreReleases() bool

	// Events
	OnInstallExtension() basecommon.Event[*InstallExtensionEvent]
	OnDidInstallExtensions() basecommon.Event[[]*InstallExtensionResult]
	OnUninstallExtension() basecommon.Event[*UninstallExtensionEvent]
	OnDidUninstallExtension() basecommon.Event[*DidUninstallExtensionEvent]
	OnDidUpdateExtensionMetadata() basecommon.Event[*DidUpdateExtensionMetadata]

	// Core operations
	CanInstall(extension *IGalleryExtension) (bool, string, error)
	InstallFromGallery(extension *IGalleryExtension, options *InstallOptions) (*ILocalExtension, error)
	InstallGalleryExtensions(extensions []*InstallExtensionInfo) ([]*InstallExtensionResult, error)
	Uninstall(extension *ILocalExtension, options *UninstallOptions) error
	UninstallExtensions(extensions []*UninstallExtensionInfo) error
	ToggleApplicationScope(extension *ILocalExtension, fromProfileLocation basecommon.URI) (*ILocalExtension, error)
	GetExtensionsControlManifest() (*IExtensionsControlManifest, error)
	ResetPinnedStateForAllUserExtensions(pinned bool) error
	RegisterParticipant(participant IExtensionManagementParticipant)
	GetTargetPlatform() (extensionscommon.TargetPlatform, error)
	Zip(extension *ILocalExtension) (basecommon.URI, error)
	GetManifest(vsix basecommon.URI) (*extensionscommon.IExtensionManifest, error)
	Install(vsix basecommon.URI, options *InstallOptions) (*ILocalExtension, error)
	InstallFromLocation(location, profileLocation basecommon.URI) (*ILocalExtension, error)
	InstallExtensionsFromProfile(extensions []extensionscommon.IExtensionIdentifier, fromProfileLocation, toProfileLocation basecommon.URI) ([]*ILocalExtension, error)
	GetInstalled(extensionType *extensionscommon.ExtensionType, profileLocation *basecommon.URI, productVersion *IProductVersion) ([]*ILocalExtension, error)
	CopyExtensions(fromProfileLocation, toProfileLocation basecommon.URI) error
	Download(extension *IGalleryExtension, operation InstallOperation, donotVerifySignature bool) (basecommon.URI, error)
	CleanUp() error
	UpdateMetadata(local *ILocalExtension, metadata *Metadata, profileLocation basecommon.URI) (*ILocalExtension, error)

	// Disposable
	Dispose()
}

// Common extension management service base implementation
type CommonExtensionManagementService struct {
	basecommon.Disposable

	productService           productcommon.IProductService
	allowedExtensionsService IAllowedExtensionsService
	PreferPreReleases        bool
}

func NewCommonExtensionManagementService(
	productService productcommon.IProductService,
	allowedExtensionsService IAllowedExtensionsService,
) *CommonExtensionManagementService {
	service := &CommonExtensionManagementService{
		productService:           productService,
		allowedExtensionsService: allowedExtensionsService,
		PreferPreReleases:        productService.GetQuality() != "stable",
	}
	return service
}

func (c *CommonExtensionManagementService) GetPreferPreReleases() bool {
	return c.PreferPreReleases
}

func (c *CommonExtensionManagementService) CanInstall(extension *IGalleryExtension) (bool, string, error) {
	allowedToInstall, reason := c.allowedExtensionsService.IsAllowed(&extensionscommon.IExtensionIdentifier{
		ID:   extension.Identifier.ID,
		UUID: &extension.Identifier.UUID,
	})
	if !allowedToInstall {
		return false, fmt.Sprintf(nls.Localize("notAllowedToInstall", "This extension cannot be installed because %s"), reason), nil
	}

	compatible, err := c.isExtensionPlatformCompatible(extension)
	if err != nil {
		return false, "", err
	}
	if !compatible {
		targetPlatform, err := c.getTargetPlatform()
		if err != nil {
			return false, "", err
		}
		return false, fmt.Sprintf(
			nls.Localize("incompatiblePlatform", "The '%s' extension is not available in %s for the %s."),
			extension.DisplayName,
			c.productService.GetNameLong(),
			targetPlatform.String(),
		), nil
	}

	return true, "", nil
}

func (c *CommonExtensionManagementService) isExtensionPlatformCompatible(extension *IGalleryExtension) (bool, error) {
	currentTargetPlatform, err := c.getTargetPlatform()
	if err != nil {
		return false, err
	}

	for _, targetPlatform := range extension.AllTargetPlatforms {
		if IsTargetPlatformCompatible(targetPlatform, extension.AllTargetPlatforms, currentTargetPlatform) {
			return true, nil
		}
	}
	return false, nil
}

func (c *CommonExtensionManagementService) getTargetPlatform() (extensionscommon.TargetPlatform, error) {
	// This should be implemented by concrete implementations
	return extensionscommon.GetCurrentTargetPlatform(), nil
}

// Abstract extension management service
type AbstractExtensionManagementService struct {
	*CommonExtensionManagementService

	galleryService          IExtensionGalleryService
	telemetryService        telemetrycommon.ITelemetryService
	uriIdentityService      uriidentitycommon.IUriIdentityService
	logService              logcommon.ILogService
	userDataProfilesService userdataprofilecommon.IUserDataProfilesService

	// State management
	extensionsControlManifest *IExtensionsControlManifest
	lastReportTimestamp       int64
	installingExtensions      sync.Map // map[string]*installTaskInfo
	uninstallingExtensions    sync.Map // map[string]IUninstallExtensionTask
	participants              []IExtensionManagementParticipant
	mu                        sync.RWMutex

	// Events
	onInstallExtension           *basecommon.Emitter[*InstallExtensionEvent]
	onDidInstallExtensions       *basecommon.Emitter[[]*InstallExtensionResult]
	onUninstallExtension         *basecommon.Emitter[*UninstallExtensionEvent]
	onDidUninstallExtension      *basecommon.Emitter[*DidUninstallExtensionEvent]
	onDidUpdateExtensionMetadata *basecommon.Emitter[*DidUpdateExtensionMetadata]
}

type installTaskInfo struct {
	Task         IInstallExtensionTask
	WaitingTasks []IInstallExtensionTask
}

func NewAbstractExtensionManagementService(
	galleryService IExtensionGalleryService,
	telemetryService telemetrycommon.ITelemetryService,
	uriIdentityService uriidentitycommon.IUriIdentityService,
	logService logcommon.ILogService,
	productService productcommon.IProductService,
	allowedExtensionsService IAllowedExtensionsService,
	userDataProfilesService userdataprofilecommon.IUserDataProfilesService,
) *AbstractExtensionManagementService {
	common := NewCommonExtensionManagementService(productService, allowedExtensionsService)

	service := &AbstractExtensionManagementService{
		CommonExtensionManagementService: common,
		galleryService:                   galleryService,
		telemetryService:                 telemetryService,
		uriIdentityService:               uriIdentityService,
		logService:                       logService,
		userDataProfilesService:          userDataProfilesService,
		participants:                     make([]IExtensionManagementParticipant, 0),
		onInstallExtension:               basecommon.NewEmitter[*InstallExtensionEvent](),
		onDidInstallExtensions:           basecommon.NewEmitter[[]*InstallExtensionResult](),
		onUninstallExtension:             basecommon.NewEmitter[*UninstallExtensionEvent](),
		onDidUninstallExtension:          basecommon.NewEmitter[*DidUninstallExtensionEvent](),
		onDidUpdateExtensionMetadata:     basecommon.NewEmitter[*DidUpdateExtensionMetadata](),
	}

	return service
}

// Event accessors
func (a *AbstractExtensionManagementService) OnInstallExtension() basecommon.Event[*InstallExtensionEvent] {
	return a.onInstallExtension.Event()
}

func (a *AbstractExtensionManagementService) OnDidInstallExtensions() basecommon.Event[[]*InstallExtensionResult] {
	return a.onDidInstallExtensions.Event()
}

func (a *AbstractExtensionManagementService) OnUninstallExtension() basecommon.Event[*UninstallExtensionEvent] {
	return a.onUninstallExtension.Event()
}

func (a *AbstractExtensionManagementService) OnDidUninstallExtension() basecommon.Event[*DidUninstallExtensionEvent] {
	return a.onDidUninstallExtension.Event()
}

func (a *AbstractExtensionManagementService) OnDidUpdateExtensionMetadata() basecommon.Event[*DidUpdateExtensionMetadata] {
	return a.onDidUpdateExtensionMetadata.Event()
}

// Core implementation methods
func (a *AbstractExtensionManagementService) InstallFromGallery(extension *IGalleryExtension, options *InstallOptions) (*ILocalExtension, error) {
	if options == nil {
		options = &InstallOptions{}
	}

	results, err := a.InstallGalleryExtensions([]*InstallExtensionInfo{{Extension: extension, Options: *options}})
	if err != nil {
		return nil, ToExtensionManagementError(err, ExtensionManagementErrorCodeUnknown)
	}

	for _, result := range results {
		if AreSameExtensions(result.Identifier, extension.Identifier) {
			if result.Local != nil {
				return result.Local, nil
			}
			if result.Error != nil {
				return nil, result.Error
			}
		}
	}

	return nil, NewExtensionManagementError(
		fmt.Sprintf("Unknown error while installing extension %s", extension.Identifier.ID),
		ExtensionManagementErrorCodeUnknown,
	)
}

func (a *AbstractExtensionManagementService) InstallGalleryExtensions(extensions []*InstallExtensionInfo) ([]*InstallExtensionResult, error) {
	if !a.galleryService.IsEnabled() {
		return nil, NewExtensionManagementError(
			nls.Localize("marketPlaceDisabled", "Marketplace is not enabled"),
			ExtensionManagementErrorCodeNotAllowed,
		)
	}

	results := make([]*InstallExtensionResult, 0)
	installableExtensions := make([]*InstallableExtension, 0)

	// Check compatibility for all extensions
	for _, extensionInfo := range extensions {
		compatible, manifest, err := a.checkAndGetCompatibleVersion(
			extensionInfo.Extension,
			extensionInfo.Options.InstallGivenVersion,
			extensionInfo.Options.InstallPreReleaseVersion,
			extensionInfo.Options.ProductVersion,
		)
		if err != nil {
			profileLocation := extensionInfo.Options.ProfileLocation
			if profileLocation == nil {
				defaultProfileLocation := a.getCurrentExtensionsManifestLocation()
				profileLocation = &defaultProfileLocation
			}
			results = append(results, &InstallExtensionResult{
				Identifier:      extensionInfo.Extension.Identifier,
				Operation:       InstallOperationInstall,
				Source:          extensionInfo.Extension,
				Error:           err,
				ProfileLocation: *profileLocation,
			})
		} else {
			installableExtensions = append(installableExtensions, &InstallableExtension{
				Manifest:  manifest,
				Extension: compatible,
				Options:   extensionInfo.Options,
			})
		}
	}

	if len(installableExtensions) > 0 {
		installResults, err := a.installExtensions(installableExtensions)
		if err != nil {
			return nil, err
		}
		results = append(results, installResults...)
	}

	return results, nil
}

func (a *AbstractExtensionManagementService) RegisterParticipant(participant IExtensionManagementParticipant) {
	a.mu.Lock()
	defer a.mu.Unlock()
	a.participants = append(a.participants, participant)
}

func (a *AbstractExtensionManagementService) GetExtensionsControlManifest() (*IExtensionsControlManifest, error) {
	if a.extensionsControlManifest == nil {
		manifest, err := a.updateControlCache()
		if err != nil {
			return nil, err
		}
		a.extensionsControlManifest = manifest
	}
	return a.extensionsControlManifest, nil
}

// Protected methods that need to be implemented by concrete classes
func (a *AbstractExtensionManagementService) getCurrentExtensionsManifestLocation() basecommon.URI {
	panic("getCurrentExtensionsManifestLocation must be implemented by concrete class")
}

func (a *AbstractExtensionManagementService) createInstallExtensionTask(manifest *extensionscommon.IExtensionManifest, extension interface{}, options InstallExtensionTaskOptions) IInstallExtensionTask {
	panic("createInstallExtensionTask must be implemented by concrete class")
}

func (a *AbstractExtensionManagementService) createUninstallExtensionTask(extension *ILocalExtension, options UninstallExtensionTaskOptions) IUninstallExtensionTask {
	panic("createUninstallExtensionTask must be implemented by concrete class")
}

func (a *AbstractExtensionManagementService) copyExtension(extension *ILocalExtension, fromProfileLocation, toProfileLocation basecommon.URI, metadata *Metadata) (*ILocalExtension, error) {
	panic("copyExtension must be implemented by concrete class")
}

func (a *AbstractExtensionManagementService) moveExtension(extension *ILocalExtension, fromProfileLocation, toProfileLocation basecommon.URI, metadata *Metadata) (*ILocalExtension, error) {
	panic("moveExtension must be implemented by concrete class")
}

func (a *AbstractExtensionManagementService) removeExtension(extension *ILocalExtension, fromProfileLocation basecommon.URI) error {
	panic("removeExtension must be implemented by concrete class")
}

func (a *AbstractExtensionManagementService) deleteExtension(extension *ILocalExtension) error {
	panic("deleteExtension must be implemented by concrete class")
}

// Helper methods
func (a *AbstractExtensionManagementService) checkAndGetCompatibleVersion(
	extension *IGalleryExtension,
	sameVersion, installPreRelease bool,
	productVersion *IProductVersion,
) (*IGalleryExtension, *extensionscommon.IExtensionManifest, error) {
	// This is a simplified implementation
	// In the full implementation, this would check compatibility and get manifest
	return extension, &extension.Manifest, nil
}

func (a *AbstractExtensionManagementService) installExtensions(extensions []*InstallableExtension) ([]*InstallExtensionResult, error) {
	// This is a simplified implementation
	// In the full implementation, this would handle the complex installation logic
	results := make([]*InstallExtensionResult, 0)

	for _, ext := range extensions {
		// Create install task
		profileLocation := ext.Options.ProfileLocation
		if profileLocation == nil {
			defaultProfileLocation := a.getCurrentExtensionsManifestLocation()
			profileLocation = &defaultProfileLocation
		}

		task := a.createInstallExtensionTask(ext.Manifest, ext.Extension, InstallExtensionTaskOptions{
			InstallOptions:  ext.Options,
			ProfileLocation: *profileLocation,
			ProductVersion:  *ext.Options.ProductVersion,
		})

		// Execute task
		local, err := task.Run()
		if err != nil {
			results = append(results, &InstallExtensionResult{
				Identifier:      task.GetIdentifier(),
				Operation:       task.GetOperation(),
				Source:          task.GetSource(),
				Error:           err,
				ProfileLocation: *profileLocation,
			})
		} else {
			results = append(results, &InstallExtensionResult{
				Identifier:      task.GetIdentifier(),
				Operation:       task.GetOperation(),
				Source:          task.GetSource(),
				Local:           local,
				ProfileLocation: *profileLocation,
			})
		}
	}

	return results, nil
}

func (a *AbstractExtensionManagementService) updateControlCache() (*IExtensionsControlManifest, error) {
	// Placeholder implementation
	return &IExtensionsControlManifest{
		Malicious: make([]string, 0),
	}, nil
}

// Abstract task base class
type AbstractExtensionTask[T any] struct {
	barrier            *sync.WaitGroup
	cancellablePromise *basecommon.CancellablePromise[T]
	mu                 sync.Mutex
}

func NewAbstractExtensionTask[T any]() *AbstractExtensionTask[T] {
	return &AbstractExtensionTask[T]{
		barrier: &sync.WaitGroup{},
	}
}

func (t *AbstractExtensionTask[T]) WaitUntilTaskIsFinished() (T, error) {
	t.barrier.Wait()
	if t.cancellablePromise != nil {
		return t.cancellablePromise.Value()
	}
	var zero T
	return zero, fmt.Errorf("task not started")
}

func (t *AbstractExtensionTask[T]) Run() (T, error) {
	t.mu.Lock()
	defer t.mu.Unlock()

	if t.cancellablePromise == nil {
		t.barrier.Add(1)
		t.cancellablePromise = basecommon.CreateCancellablePromise(func(token basecommon.CancellationToken) (T, error) {
			defer t.barrier.Done()
			return t.doRun(token)
		})
	}

	return t.cancellablePromise.Value()
}

func (t *AbstractExtensionTask[T]) Cancel() {
	t.mu.Lock()
	defer t.mu.Unlock()

	if t.cancellablePromise != nil {
		t.cancellablePromise.Cancel()
	}
}

func (t *AbstractExtensionTask[T]) doRun(token basecommon.CancellationToken) (T, error) {
	panic("doRun must be implemented by concrete task")
}

// Utility functions are now in extensionManagement.go
