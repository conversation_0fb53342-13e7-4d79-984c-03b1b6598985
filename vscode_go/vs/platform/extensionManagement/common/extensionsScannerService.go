/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"time"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	extensionscommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/extensions/common"
)

// Scanner service types and options
type ScanOptions struct {
	IncludeInvalid bool    `json:"includeInvalid,omitempty"`
	Language       *string `json:"language,omitempty"`
}

type SystemExtensionsScanOptions struct {
	ScanOptions
	UseDevMode bool `json:"useDevMode,omitempty"`
}

type UserExtensionsScanOptions struct {
	ScanOptions
	ProfileLocation    basecommon.URI  `json:"profileLocation"`
	ProductVersion     IProductVersion `json:"productVersion"`
	IncludeUninstalled bool            `json:"includeUninstalled,omitempty"`
}

// Manifest metadata
type ManifestMetadata struct {
	ID                  *string                         `json:"id,omitempty"`
	PublisherID         *string                         `json:"publisherId,omitempty"`
	PublisherName       *string                         `json:"publisherName,omitempty"`
	IsPreReleaseVersion bool                            `json:"isPreReleaseVersion,omitempty"`
	TargetPlatform      extensionscommon.TargetPlatform `json:"targetPlatform,omitempty"`
	Updated             bool                            `json:"updated,omitempty"`
	IsApplicationScoped bool                            `json:"isApplicationScoped,omitempty"`
	IsMachineScoped     bool                            `json:"isMachineScoped,omitempty"`
	IsBuiltin           bool                            `json:"isBuiltin,omitempty"`
	IsSystem            bool                            `json:"isSystem,omitempty"`
	InstallTimestamp    *time.Time                      `json:"installTimestamp,omitempty"`
	PreRelease          bool                            `json:"preRelease,omitempty"`
	Pinned              bool                            `json:"pinned,omitempty"`
	Size                int64                           `json:"size,omitempty"`
}

// Scanned extension interface
type IScannedExtension struct {
	Identifier     extensionscommon.IExtensionIdentifier `json:"identifier"`
	Location       basecommon.URI                        `json:"location"`
	Manifest       *extensionscommon.IExtensionManifest  `json:"manifest"`
	Type           extensionscommon.ExtensionType        `json:"type"`
	IsBuiltin      bool                                  `json:"isBuiltin"`
	ReadmeURL      *basecommon.URI                       `json:"readmeUrl,omitempty"`
	ChangelogURL   *basecommon.URI                       `json:"changelogUrl,omitempty"`
	Metadata       *Metadata                             `json:"metadata,omitempty"`
	TargetPlatform extensionscommon.TargetPlatform       `json:"targetPlatform"`
	Validations    []ValidationIssue                     `json:"validations"`
	IsValid        bool                                  `json:"isValid"`
	PreRelease     bool                                  `json:"preRelease,omitempty"`
}

type ValidationIssue struct {
	Severity basecommon.Severity `json:"severity"`
	Message  string              `json:"message"`
}

// Extensions scanner service interface
type IExtensionsScannerService interface {
	// Service brand
	ServiceBrand() interface{}

	// Properties
	GetSystemExtensionsLocation() basecommon.URI
	GetUserExtensionsLocation() basecommon.URI
	OnDidChangeCache() basecommon.Event[extensionscommon.ExtensionType]

	// Core scanning methods
	ScanAllExtensions(systemScanOptions SystemExtensionsScanOptions, userScanOptions UserExtensionsScanOptions) ([]*IScannedExtension, error)
	ScanSystemExtensions(scanOptions SystemExtensionsScanOptions) ([]*IScannedExtension, error)
	ScanUserExtensions(scanOptions UserExtensionsScanOptions) ([]*IScannedExtension, error)
	ScanAllUserExtensions() ([]*IScannedExtension, error)

	// Extension-specific scanning
	ScanExtensionsUnderDevelopment(existingExtensions []*IScannedExtension, scanOptions ScanOptions) ([]*IScannedExtension, error)
	ScanExistingExtension(extensionLocation basecommon.URI, extensionType extensionscommon.ExtensionType, scanOptions ScanOptions) (*IScannedExtension, error)
	ScanMultipleExtensions(extensionLocations []basecommon.URI, extensionType extensionscommon.ExtensionType, scanOptions ScanOptions) ([]*IScannedExtension, error)
	ScanOneOrMultipleExtensions(extensionLocation basecommon.URI, extensionType extensionscommon.ExtensionType, scanOptions ScanOptions) ([]*IScannedExtension, error)

	// Metadata management
	UpdateManifestMetadata(extensionLocation basecommon.URI, metadata ManifestMetadata) error
	InitializeDefaultProfileExtensions() error

	// Disposable
	Dispose()
}

// Base scanner service implementation
type BaseExtensionsScannerService struct {
	basecommon.Disposable

	systemExtensionsLocation basecommon.URI
	userExtensionsLocation   basecommon.URI
	onDidChangeCache         *basecommon.Emitter[extensionscommon.ExtensionType]
}

func NewBaseExtensionsScannerService(
	systemExtensionsLocation basecommon.URI,
	userExtensionsLocation basecommon.URI,
) *BaseExtensionsScannerService {
	service := &BaseExtensionsScannerService{
		systemExtensionsLocation: systemExtensionsLocation,
		userExtensionsLocation:   userExtensionsLocation,
		onDidChangeCache:         basecommon.NewEmitter[extensionscommon.ExtensionType](),
	}
	return service
}

func (b *BaseExtensionsScannerService) ServiceBrand() interface{} {
	return "IExtensionsScannerService"
}

func (b *BaseExtensionsScannerService) GetSystemExtensionsLocation() basecommon.URI {
	return b.systemExtensionsLocation
}

func (b *BaseExtensionsScannerService) GetUserExtensionsLocation() basecommon.URI {
	return b.userExtensionsLocation
}

func (b *BaseExtensionsScannerService) OnDidChangeCache() basecommon.Event[extensionscommon.ExtensionType] {
	return b.onDidChangeCache.Event()
}

// Default implementations that should be overridden by concrete implementations
func (b *BaseExtensionsScannerService) ScanAllExtensions(systemScanOptions SystemExtensionsScanOptions, userScanOptions UserExtensionsScanOptions) ([]*IScannedExtension, error) {
	systemExtensions, err := b.ScanSystemExtensions(systemScanOptions)
	if err != nil {
		return nil, err
	}

	userExtensions, err := b.ScanUserExtensions(userScanOptions)
	if err != nil {
		return nil, err
	}

	// Combine and return
	allExtensions := make([]*IScannedExtension, 0, len(systemExtensions)+len(userExtensions))
	allExtensions = append(allExtensions, systemExtensions...)
	allExtensions = append(allExtensions, userExtensions...)

	return allExtensions, nil
}

func (b *BaseExtensionsScannerService) ScanSystemExtensions(scanOptions SystemExtensionsScanOptions) ([]*IScannedExtension, error) {
	// Override in concrete implementations
	return []*IScannedExtension{}, nil
}

func (b *BaseExtensionsScannerService) ScanUserExtensions(scanOptions UserExtensionsScanOptions) ([]*IScannedExtension, error) {
	// Override in concrete implementations
	return []*IScannedExtension{}, nil
}

func (b *BaseExtensionsScannerService) ScanAllUserExtensions() ([]*IScannedExtension, error) {
	// This would need appropriate default scan options
	// Override in concrete implementations with proper defaults
	return []*IScannedExtension{}, nil
}

func (b *BaseExtensionsScannerService) ScanExtensionsUnderDevelopment(existingExtensions []*IScannedExtension, scanOptions ScanOptions) ([]*IScannedExtension, error) {
	// Override in concrete implementations
	return []*IScannedExtension{}, nil
}

func (b *BaseExtensionsScannerService) ScanExistingExtension(extensionLocation basecommon.URI, extensionType extensionscommon.ExtensionType, scanOptions ScanOptions) (*IScannedExtension, error) {
	// Override in concrete implementations
	return nil, nil
}

func (b *BaseExtensionsScannerService) ScanMultipleExtensions(extensionLocations []basecommon.URI, extensionType extensionscommon.ExtensionType, scanOptions ScanOptions) ([]*IScannedExtension, error) {
	// Override in concrete implementations
	return []*IScannedExtension{}, nil
}

func (b *BaseExtensionsScannerService) ScanOneOrMultipleExtensions(extensionLocation basecommon.URI, extensionType extensionscommon.ExtensionType, scanOptions ScanOptions) ([]*IScannedExtension, error) {
	// Override in concrete implementations
	return []*IScannedExtension{}, nil
}

func (b *BaseExtensionsScannerService) UpdateManifestMetadata(extensionLocation basecommon.URI, metadata ManifestMetadata) error {
	// Override in concrete implementations
	return nil
}

func (b *BaseExtensionsScannerService) InitializeDefaultProfileExtensions() error {
	// Override in concrete implementations
	return nil
}

// Web Extensions Scanner Service Interface (for browser environments)
type IWebExtensionsScannerService interface {
	// Service brand
	ServiceBrand() interface{}

	// Core scanning methods
	ScanSystemExtensions() ([]*extensionscommon.IExtension, error)
	ScanUserExtensions(profileLocation basecommon.URI, options *ScanOptions) ([]*IScannedExtension, error)
	ScanExtensionsUnderDevelopment() ([]*extensionscommon.IExtension, error)
	ScanExistingExtension(extensionLocation basecommon.URI, extensionType extensionscommon.ExtensionType, profileLocation basecommon.URI) (*IScannedExtension, error)

	// Extension management
	AddExtension(location basecommon.URI, metadata Metadata, profileLocation basecommon.URI) (*IScannedExtension, error)
	AddExtensionFromGallery(galleryExtension *IGalleryExtension, metadata Metadata, profileLocation basecommon.URI) (*IScannedExtension, error)
	RemoveExtension(extension *IScannedExtension, profileLocation basecommon.URI) error
	CopyExtensions(fromProfileLocation, toProfileLocation basecommon.URI, filter func(*IScannedExtension) bool) error

	// Metadata management
	UpdateMetadata(extension *IScannedExtension, metadata *Metadata, profileLocation basecommon.URI) (*IScannedExtension, error)

	// Manifest scanning
	ScanExtensionManifest(extensionLocation basecommon.URI) (*extensionscommon.IExtensionManifest, error)

	// Disposable
	Dispose()
}

// Base web extensions scanner service
type BaseWebExtensionsScannerService struct {
	basecommon.Disposable
}

func NewBaseWebExtensionsScannerService() *BaseWebExtensionsScannerService {
	return &BaseWebExtensionsScannerService{}
}

func (b *BaseWebExtensionsScannerService) ServiceBrand() interface{} {
	return "IWebExtensionsScannerService"
}

// Default implementations - override in concrete implementations
func (b *BaseWebExtensionsScannerService) ScanSystemExtensions() ([]*extensionscommon.IExtension, error) {
	return []*extensionscommon.IExtension{}, nil
}

func (b *BaseWebExtensionsScannerService) ScanUserExtensions(profileLocation basecommon.URI, options *ScanOptions) ([]*IScannedExtension, error) {
	return []*IScannedExtension{}, nil
}

func (b *BaseWebExtensionsScannerService) ScanExtensionsUnderDevelopment() ([]*extensionscommon.IExtension, error) {
	return []*extensionscommon.IExtension{}, nil
}

func (b *BaseWebExtensionsScannerService) ScanExistingExtension(extensionLocation basecommon.URI, extensionType extensionscommon.ExtensionType, profileLocation basecommon.URI) (*IScannedExtension, error) {
	return nil, nil
}

func (b *BaseWebExtensionsScannerService) AddExtension(location basecommon.URI, metadata Metadata, profileLocation basecommon.URI) (*IScannedExtension, error) {
	return nil, nil
}

func (b *BaseWebExtensionsScannerService) AddExtensionFromGallery(galleryExtension *IGalleryExtension, metadata Metadata, profileLocation basecommon.URI) (*IScannedExtension, error) {
	return nil, nil
}

func (b *BaseWebExtensionsScannerService) RemoveExtension(extension *IScannedExtension, profileLocation basecommon.URI) error {
	return nil
}

func (b *BaseWebExtensionsScannerService) CopyExtensions(fromProfileLocation, toProfileLocation basecommon.URI, filter func(*IScannedExtension) bool) error {
	return nil
}

func (b *BaseWebExtensionsScannerService) UpdateMetadata(extension *IScannedExtension, metadata *Metadata, profileLocation basecommon.URI) (*IScannedExtension, error) {
	return extension, nil
}

func (b *BaseWebExtensionsScannerService) ScanExtensionManifest(extensionLocation basecommon.URI) (*extensionscommon.IExtensionManifest, error) {
	return nil, nil
}

// Helper functions for extension scanning
func FilterExtensionsByType(extensions []*IScannedExtension, extensionType extensionscommon.ExtensionType) []*IScannedExtension {
	filtered := make([]*IScannedExtension, 0)
	for _, ext := range extensions {
		if ext.Type == extensionType {
			filtered = append(filtered, ext)
		}
	}
	return filtered
}

func FindExtensionByIdentifier(extensions []*IScannedExtension, identifier extensionscommon.IExtensionIdentifier) *IScannedExtension {
	for _, ext := range extensions {
		if AreSameExtensions(ext.Identifier, identifier) {
			return ext
		}
	}
	return nil
}

func ValidateExtensionManifest(manifest *extensionscommon.IExtensionManifest) []ValidationIssue {
	var issues []ValidationIssue

	if manifest.Name == "" {
		issues = append(issues, ValidationIssue{
			Severity: basecommon.SeverityError,
			Message:  "Extension name is required",
		})
	}

	if manifest.Publisher == "" {
		issues = append(issues, ValidationIssue{
			Severity: basecommon.SeverityError,
			Message:  "Extension publisher is required",
		})
	}

	if manifest.Version == "" {
		issues = append(issues, ValidationIssue{
			Severity: basecommon.SeverityError,
			Message:  "Extension version is required",
		})
	}

	return issues
}
