/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"strings"
	"sync"
	"time"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	extensionmanagementcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/extensionManagement/common"
	logcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
	userdataprofilecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/userDataProfile/common"
)

const (
	MaxScriptTimeout = 30 * time.Second
	NodeExecutable   = "node"
)

// ExtensionsLifecycle handles post-install and post-uninstall lifecycle scripts
type ExtensionsLifecycle struct {
	basecommon.Disposable

	// Dependencies
	logService              logcommon.ILogService
	userDataProfilesService userdataprofilecommon.IUserDataProfilesService

	// Concurrency control
	limiter *AsyncLimiter
	mu      sync.RWMutex
}

// AsyncLimiter provides concurrency limiting for lifecycle operations
type AsyncLimiter struct {
	semaphore chan struct{}
	mu        sync.Mutex
}

// NewAsyncLimiter creates a new AsyncLimiter with the specified concurrency limit
func NewAsyncLimiter(limit int) *AsyncLimiter {
	return &AsyncLimiter{
		semaphore: make(chan struct{}, limit),
	}
}

// Queue queues a function to run with concurrency limiting
func (l *AsyncLimiter) Queue(ctx context.Context, fn func() error) error {
	select {
	case l.semaphore <- struct{}{}:
		defer func() { <-l.semaphore }()
		return fn()
	case <-ctx.Done():
		return ctx.Err()
	}
}

// ScriptInfo represents a parsed lifecycle script
type ScriptInfo struct {
	Script string
	Args   []string
}

// NewExtensionsLifecycle creates a new ExtensionsLifecycle instance
func NewExtensionsLifecycle(
	logService logcommon.ILogService,
	userDataProfilesService userdataprofilecommon.IUserDataProfilesService,
) *ExtensionsLifecycle {
	return &ExtensionsLifecycle{
		logService:              logService,
		userDataProfilesService: userDataProfilesService,
		limiter:                 NewAsyncLimiter(1), // Serialize lifecycle operations
	}
}

// PostInstall runs post-install lifecycle scripts for an extension
func (el *ExtensionsLifecycle) PostInstall(
	ctx context.Context,
	extension *extensionmanagementcommon.ILocalExtension,
) error {
	return el.runLifecycleScript(ctx, extension, "install")
}

// PostUninstall runs post-uninstall lifecycle scripts for an extension
func (el *ExtensionsLifecycle) PostUninstall(
	ctx context.Context,
	extension *extensionmanagementcommon.ILocalExtension,
) error {
	return el.runLifecycleScript(ctx, extension, "uninstall")
}

// runLifecycleScript runs a lifecycle script for the specified type
func (el *ExtensionsLifecycle) runLifecycleScript(
	ctx context.Context,
	extension *extensionmanagementcommon.ILocalExtension,
	lifecycleType string,
) error {
	el.mu.Lock()
	defer el.mu.Unlock()

	el.logService.Info(fmt.Sprintf("Running %s lifecycle for %s@%s",
		lifecycleType, extension.Identifier.ID, extension.Manifest.Version))

	scriptInfo := el.parseScript(extension, lifecycleType)
	if scriptInfo == nil {
		el.logService.Trace(fmt.Sprintf("No %s script found for %s",
			lifecycleType, extension.Identifier.ID))
		return nil
	}

	return el.limiter.Queue(ctx, func() error {
		return el.executeScript(ctx, extension, scriptInfo, lifecycleType)
	})
}

// parseScript parses a lifecycle script from extension manifest
func (el *ExtensionsLifecycle) parseScript(
	extension *extensionmanagementcommon.ILocalExtension,
	scriptType string,
) *ScriptInfo {
	// Check if extension location is a file scheme and has manifest
	if extension.Location.Scheme != "file" {
		return nil
	}

	// Check if manifest has scripts section
	// Note: Scripts field might not be available in IExtensionManifest
	// This is a simplified implementation - in real usage, you'd need to
	// access the raw manifest data or extend the interface

	// For now, return nil as lifecycle scripts are not commonly used
	return nil
}

// executeScript executes a lifecycle script
func (el *ExtensionsLifecycle) executeScript(
	ctx context.Context,
	extension *extensionmanagementcommon.ILocalExtension,
	scriptInfo *ScriptInfo,
	lifecycleType string,
) error {
	el.logService.Trace(fmt.Sprintf("Executing %s script: %s %v",
		lifecycleType, scriptInfo.Script, scriptInfo.Args))

	// Create context with timeout
	scriptCtx, cancel := context.WithTimeout(ctx, MaxScriptTimeout)
	defer cancel()

	// Prepare command arguments
	args := []string{fmt.Sprintf("--type=extension-post-%s", lifecycleType)}
	args = append(args, scriptInfo.Args...)

	// Create command
	cmd := exec.CommandContext(scriptCtx, NodeExecutable, append([]string{scriptInfo.Script}, args...)...)

	// Set working directory to extension root
	cmd.Dir = extension.Location.FSPath()

	// Set up environment
	cmd.Env = os.Environ()

	// Capture output
	stdout, err := cmd.StdoutPipe()
	if err != nil {
		return fmt.Errorf("failed to create stdout pipe: %w", err)
	}

	stderr, err := cmd.StderrPipe()
	if err != nil {
		return fmt.Errorf("failed to create stderr pipe: %w", err)
	}

	// Start the process
	if err := cmd.Start(); err != nil {
		return fmt.Errorf("failed to start script: %w", err)
	}

	// Log output in goroutines
	var wg sync.WaitGroup
	wg.Add(2)

	go func() {
		defer wg.Done()
		el.logOutput(stdout, extension, lifecycleType, false)
	}()

	go func() {
		defer wg.Done()
		el.logOutput(stderr, extension, lifecycleType, true)
	}()

	// Wait for the process to complete
	err = cmd.Wait()

	// Wait for output logging to complete
	wg.Wait()

	if err != nil {
		if scriptCtx.Err() == context.DeadlineExceeded {
			el.logService.Error(extension.Identifier.ID, extension.Manifest.Version,
				fmt.Sprintf("post-%s script timed out after %v", lifecycleType, MaxScriptTimeout))
			return fmt.Errorf("script timed out")
		}

		el.logService.Error(extension.Identifier.ID, extension.Manifest.Version,
			fmt.Sprintf("post-%s script failed: %v", lifecycleType, err))
		return fmt.Errorf("script execution failed: %w", err)
	}

	el.logService.Info(fmt.Sprintf("Successfully completed %s lifecycle for %s@%s",
		lifecycleType, extension.Identifier.ID, extension.Manifest.Version))

	return nil
}

// logOutput logs script output
func (el *ExtensionsLifecycle) logOutput(
	reader interface{ Read([]byte) (int, error) },
	extension *extensionmanagementcommon.ILocalExtension,
	lifecycleType string,
	isError bool,
) {
	buf := make([]byte, 1024)
	for {
		n, err := reader.Read(buf)
		if n > 0 {
			output := strings.TrimSpace(string(buf[:n]))
			if output != "" {
				logMsg := fmt.Sprintf("post-%s: %s", lifecycleType, output)
				if isError {
					el.logService.Error(extension.Identifier.ID, extension.Manifest.Version, logMsg)
				} else {
					el.logService.Info(extension.Identifier.ID, extension.Manifest.Version, logMsg)
				}
			}
		}
		if err != nil {
			break
		}
	}
}

// Dispose cleans up resources
func (el *ExtensionsLifecycle) Dispose() {
	el.Disposable.Dispose()
}
