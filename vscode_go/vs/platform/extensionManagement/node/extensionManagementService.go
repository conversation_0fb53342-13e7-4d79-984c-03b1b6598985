/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"encoding/json"
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	basesemver "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common/semver"
	basenode "github.com/yudaprama/kawai-agent/vscode_go/vs/base/node"
	"github.com/yudaprama/kawai-agent/vscode_go/vs/nls"
	downloadcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/download/common"
	extensionmanagementcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/extensionManagement/common"
	extensionscommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/extensions/common"
)

const DELETED_FOLDER_POSTFIX = ".vsctmp"

// Service interfaces (these would be injected)
type IExtensionGalleryService interface {
	// Gallery service methods
}

type ITelemetryService interface {
	// Telemetry service methods
}

type ILogService interface {
	Trace(message string, args ...interface{})
	Info(message string, args ...interface{})
	Warn(message string, args ...interface{})
	Error(message string, args ...interface{})
}

type INativeEnvironmentService interface {
	// Environment service methods
}

type IFileService interface {
	Exists(uri basecommon.URI) (bool, error)
	ReadFile(uri basecommon.URI) ([]byte, error)
	WriteFile(uri basecommon.URI, content []byte) error
	Delete(uri basecommon.URI, recursive bool) error
	CreateFolder(uri basecommon.URI) error
	Copy(source, destination basecommon.URI) error
	Move(source, destination basecommon.URI) error
}

type IExtensionsScannerService interface {
	ScanUserExtensions(location basecommon.URI) ([]extensionscommon.IExtension, error)
	ScanSystemExtensions() ([]extensionscommon.IExtension, error)
}

type IExtensionsProfileScannerService interface {
	ScanProfileExtensions(profileLocation basecommon.URI) ([]extensionscommon.IExtension, error)
}

type IProductService interface {
	GetVersion() string
	GetDate() *time.Time
}

type IAllowedExtensionsService interface {
	IsAllowed(extension extensionscommon.IExtensionIdentifier) bool
}

type IUriIdentityService interface {
	// URI identity service methods
}

type IUserDataProfilesService interface {
	GetDefaultProfile() UserProfile
}

type UserProfile struct {
	ExtensionsResource basecommon.URI
}

type IInstantiationService interface {
	CreateInstance(serviceType interface{}) (interface{}, error)
}

type IConfigurationService interface {
	GetValue(key string) interface{}
}

type IExtensionGalleryManifestService interface {
	// Extension gallery manifest service methods
}

// Main extension management service
type ExtensionManagementService struct {
	basecommon.Disposable

	// Injected services
	galleryService                  IExtensionGalleryService
	telemetryService                ITelemetryService
	logService                      ILogService
	environmentService              INativeEnvironmentService
	extensionsScannerService        IExtensionsScannerService
	extensionsProfileScannerService IExtensionsProfileScannerService
	downloadService                 downloadcommon.IDownloadServiceInterface
	instantiationService            IInstantiationService
	fileService                     IFileService
	configurationService            IConfigurationService
	extensionGalleryManifestService IExtensionGalleryManifestService
	productService                  IProductService
	allowedExtensionsService        IAllowedExtensionsService
	uriIdentityService              IUriIdentityService
	userDataProfilesService         IUserDataProfilesService

	// Internal components
	extensionsScanner     *ExtensionsScanner
	manifestCache         *ExtensionsManifestCache
	extensionsDownloader  *ExtensionsDownloader
	targetPlatformPromise *sync.Once
	targetPlatform        extensionscommon.TargetPlatform

	// State
	extractingGalleryExtensions sync.Map // map[string]chan ExtractExtensionResult
	mu                          sync.RWMutex
}

type ExtractExtensionResult struct {
	Local              *extensionmanagementcommon.ILocalExtension
	VerificationStatus *string
}

type InstallOptions struct {
	SkipValidation       bool
	DoNotVerifySignature bool
	IsBuiltin            bool
	IsSystem             bool
	ProfileLocation      *basecommon.URI
	Operation            *string
	Source               *extensionmanagementcommon.InstallSource
}

type ExtensionsManifestCache struct {
	// Manifest cache implementation
}

type ExtensionsDownloader struct {
	ExtensionsDownloadDir basecommon.URI
}

// Constructor for ExtensionManagementService
func NewExtensionManagementService(
	galleryService IExtensionGalleryService,
	telemetryService ITelemetryService,
	logService ILogService,
	environmentService INativeEnvironmentService,
	extensionsScannerService IExtensionsScannerService,
	extensionsProfileScannerService IExtensionsProfileScannerService,
	downloadService downloadcommon.IDownloadServiceInterface,
	instantiationService IInstantiationService,
	fileService IFileService,
	configurationService IConfigurationService,
	extensionGalleryManifestService IExtensionGalleryManifestService,
	productService IProductService,
	allowedExtensionsService IAllowedExtensionsService,
	uriIdentityService IUriIdentityService,
	userDataProfilesService IUserDataProfilesService,
) *ExtensionManagementService {

	service := &ExtensionManagementService{
		galleryService:                  galleryService,
		telemetryService:                telemetryService,
		logService:                      logService,
		environmentService:              environmentService,
		extensionsScannerService:        extensionsScannerService,
		extensionsProfileScannerService: extensionsProfileScannerService,
		downloadService:                 downloadService,
		instantiationService:            instantiationService,
		fileService:                     fileService,
		configurationService:            configurationService,
		extensionGalleryManifestService: extensionGalleryManifestService,
		productService:                  productService,
		allowedExtensionsService:        allowedExtensionsService,
		uriIdentityService:              uriIdentityService,
		userDataProfilesService:         userDataProfilesService,
		targetPlatformPromise:           &sync.Once{},
	}

	// Initialize internal components
	service.extensionsScanner = NewExtensionsScanner(
		service.beforeRemovingExtension,
		fileService,
		extensionsScannerService,
		extensionsProfileScannerService,
		uriIdentityService,
		logService,
	)

	service.manifestCache = &ExtensionsManifestCache{}
	service.extensionsDownloader = &ExtensionsDownloader{}

	return service
}

func (s *ExtensionManagementService) beforeRemovingExtension(extension *extensionmanagementcommon.ILocalExtension) error {
	// Extension lifecycle hook before removal
	return nil
}

// GetTargetPlatform returns the current platform
func (s *ExtensionManagementService) GetTargetPlatform() extensionscommon.TargetPlatform {
	s.targetPlatformPromise.Do(func() {
		s.targetPlatform = extensionscommon.GetCurrentTargetPlatform()
	})
	return s.targetPlatform
}

// Zip creates a zip file from an installed extension
func (s *ExtensionManagementService) Zip(extension *extensionmanagementcommon.ILocalExtension) (basecommon.URI, error) {
	s.logService.Trace("ExtensionManagementService#zip", extension.Identifier.ID)

	files, err := s.collectFiles(extension)
	if err != nil {
		return basecommon.URI{}, err
	}

	tempDir := filepath.Join(os.TempDir(), basecommon.GenerateUuid())
	location, err := basenode.Zip(tempDir, files)
	if err != nil {
		return basecommon.URI{}, err
	}

	return *basecommon.File(location), nil
}

// GetManifest extracts and returns the manifest from a VSIX file
func (s *ExtensionManagementService) GetManifest(vsix basecommon.URI) (*extensionscommon.IExtensionManifest, error) {
	location, cleanup, err := s.downloadVsix(vsix)
	if err != nil {
		return nil, err
	}
	defer cleanup()

	return s.getManifestFromPath(location.FSPath())
}

func (s *ExtensionManagementService) getManifestFromPath(zipPath string) (*extensionscommon.IExtensionManifest, error) {
	// Extract manifest from zip file
	tempDir := filepath.Join(os.TempDir(), basecommon.GenerateUuid())
	err := basenode.Extract(zipPath, tempDir, basenode.IExtractOptions{}, basecommon.CancellationTokenNone)
	if err != nil {
		return nil, err
	}
	defer os.RemoveAll(tempDir)

	// Find and parse package.json
	packageJSONPath := filepath.Join(tempDir, "package.json")
	if _, err := os.Stat(packageJSONPath); err == nil {
		if filepath.Base(packageJSONPath) == "package.json" {
			data, err := os.ReadFile(packageJSONPath)
			if err != nil {
				return nil, err
			}

			var manifest extensionscommon.IExtensionManifest
			if err := json.Unmarshal(data, &manifest); err != nil {
				return nil, err
			}

			return &manifest, nil
		}
	}

	return nil, fmt.Errorf("package.json not found in extension")
}

// GetInstalled returns installed extensions
func (s *ExtensionManagementService) GetInstalled(
	extensionType *extensionscommon.ExtensionType,
	profileLocation *basecommon.URI,
	productVersion *extensionmanagementcommon.IProductVersion,
	language *string,
) ([]*extensionmanagementcommon.ILocalExtension, error) {

	if profileLocation == nil {
		defaultProfile := s.userDataProfilesService.GetDefaultProfile()
		profileLocation = &defaultProfile.ExtensionsResource
	}

	if productVersion == nil {
		productVersion = &extensionmanagementcommon.IProductVersion{
			Version: s.productService.GetVersion(),
			Date:    s.productService.GetDate(),
		}
	}

	return s.extensionsScanner.ScanExtensions(extensionType, *profileLocation, *productVersion, language)
}

// ScanAllUserInstalledExtensions scans all user extensions
func (s *ExtensionManagementService) ScanAllUserInstalledExtensions() ([]*extensionmanagementcommon.ILocalExtension, error) {
	return s.extensionsScanner.ScanAllUserExtensions()
}

// ScanInstalledExtensionAtLocation scans extension at specific location
func (s *ExtensionManagementService) ScanInstalledExtensionAtLocation(location basecommon.URI) (*extensionmanagementcommon.ILocalExtension, error) {
	return s.extensionsScanner.ScanUserExtensionAtLocation(location)
}

// Install installs extension from VSIX file
func (s *ExtensionManagementService) Install(vsix basecommon.URI, options InstallOptions) (*extensionmanagementcommon.ILocalExtension, error) {
	s.logService.Trace("ExtensionManagementService#install", vsix.ToString())

	location, cleanup, err := s.downloadVsix(vsix)
	if err != nil {
		return nil, err
	}
	defer cleanup()

	manifest, err := s.getManifestFromPath(location.FSPath())
	if err != nil {
		return nil, err
	}

	extensionID := s.getGalleryExtensionID(manifest.Publisher, manifest.Name)

	// Validate engine compatibility
	if manifest.Engines != nil {
		if vscodeEngine, exists := manifest.Engines["vscode"]; exists {
			if !s.isEngineValid(vscodeEngine, s.productService.GetVersion()) {
				return nil, fmt.Errorf(
					nls.Localize("incompatible", "Unable to install extension '{0}' as it is not compatible with VS Code '{1}'"),
					extensionID,
					s.productService.GetVersion(),
				)
			}
		}
	}

	// Check if extension is allowed
	if !s.allowedExtensionsService.IsAllowed(extensionscommon.IExtensionIdentifier{ID: extensionID}) {
		return nil, fmt.Errorf(nls.Localize("notAllowed", "This extension cannot be installed"))
	}

	// Perform installation
	return s.installFromLocation(location, manifest, options)
}

func (s *ExtensionManagementService) installFromLocation(
	location basecommon.URI,
	manifest *extensionscommon.IExtensionManifest,
	options InstallOptions,
) (*extensionmanagementcommon.ILocalExtension, error) {

	profileLocation := options.ProfileLocation
	if profileLocation == nil {
		defaultProfile := s.userDataProfilesService.GetDefaultProfile()
		profileLocation = &defaultProfile.ExtensionsResource
	}

	// Extract extension to proper location
	extensionKey := ExtensionKey{
		ID:      s.getGalleryExtensionID(manifest.Publisher, manifest.Name),
		Version: manifest.Version,
	}

	result, err := s.extensionsScanner.ExtractUserExtension(
		extensionKey,
		location.FSPath(),
		true, // removeIfExists
		basecommon.CancellationTokenNone,
	)

	if err != nil {
		return nil, err
	}

	// Add to profile
	metadata := &extensionmanagementcommon.Metadata{}
	if options.Source != nil {
		metadata.Source = *options.Source
	} else {
		metadata.Source = extensionmanagementcommon.InstallSourceVSIX
	}

	err = s.addExtensionToProfile(result, metadata, *profileLocation)
	if err != nil {
		return nil, err
	}

	s.logService.Info("Successfully installed extension", result.Identifier.ID, profileLocation.ToString())
	return result, nil
}

// InstallFromLocation installs extension from a local directory
func (s *ExtensionManagementService) InstallFromLocation(location, profileLocation basecommon.URI) (*extensionmanagementcommon.ILocalExtension, error) {
	s.logService.Trace("ExtensionManagementService#installFromLocation", location.ToString())

	local, err := s.extensionsScanner.ScanUserExtensionAtLocation(location)
	if err != nil {
		return nil, err
	}

	if local == nil || local.Manifest.Name == "" || local.Manifest.Version == "" {
		return nil, fmt.Errorf("Cannot find a valid extension from the location %s", location.ToString())
	}

	metadata := &extensionmanagementcommon.Metadata{
		IGalleryMetadata: extensionmanagementcommon.IGalleryMetadata{
			Source: extensionmanagementcommon.InstallSourceResource,
		},
	}

	err = s.addExtensionToProfile(local, metadata, profileLocation)
	if err != nil {
		return nil, err
	}

	s.logService.Info("Successfully installed extension", local.Identifier.ID, profileLocation.ToString())
	return local, nil
}

// UpdateMetadata updates extension metadata
func (s *ExtensionManagementService) UpdateMetadata(
	local *extensionmanagementcommon.ILocalExtension,
	metadata *extensionmanagementcommon.Metadata,
	profileLocation basecommon.URI,
) (*extensionmanagementcommon.ILocalExtension, error) {
	s.logService.Trace("ExtensionManagementService#updateMetadata", local.Identifier.ID)
	return s.extensionsScanner.UpdateMetadata(local, metadata, profileLocation)
}

// DeleteExtensions removes extensions from disk
func (s *ExtensionManagementService) DeleteExtensions(extensions ...extensionscommon.IExtension) error {
	s.logService.Trace("ExtensionManagementService#deleteExtensions", len(extensions))

	for _, extension := range extensions {
		// Convert IExtension to ILocalExtension if it's a local extension
		if localExt := s.toLocalExtension(&extension); localExt != nil {
			err := s.extensionsScanner.DeleteExtension(localExt, "user")
			if err != nil {
				return err
			}
		}
	}

	return nil
}

// Helper method to convert IExtension to ILocalExtension
func (s *ExtensionManagementService) toLocalExtension(extension *extensionscommon.IExtension) *extensionmanagementcommon.ILocalExtension {
	if extension.Type == extensionscommon.ExtensionTypeUser {
		return s.extensionsScanner.toLocalExtension(extension)
	}
	return nil
}

// Helper methods

func (s *ExtensionManagementService) downloadVsix(vsix basecommon.URI) (basecommon.URI, func(), error) {
	if vsix.GetScheme() == "file" {
		return vsix, func() {}, nil
	}

	// Download to temporary location
	tempDir := filepath.Join(os.TempDir(), basecommon.GenerateUuid())
	err := os.MkdirAll(tempDir, 0755)
	if err != nil {
		return basecommon.URI{}, nil, err
	}

	tempFile := filepath.Join(tempDir, "extension.vsix")
	tempURI := *basecommon.File(tempFile)

	err = s.downloadService.Download(&vsix, &tempURI, basecommon.CancellationTokenNone)
	if err != nil {
		os.RemoveAll(tempDir)
		return basecommon.URI{}, nil, err
	}

	cleanup := func() {
		os.RemoveAll(tempDir)
	}

	return tempURI, cleanup, nil
}

func (s *ExtensionManagementService) collectFiles(extension *extensionmanagementcommon.ILocalExtension) ([]basenode.IFile, error) {
	var files []basenode.IFile

	err := filepath.WalkDir(extension.Location.FSPath(), func(path string, d fs.DirEntry, err error) error {
		if err != nil {
			return err
		}

		if !d.IsDir() {
			relPath, err := filepath.Rel(extension.Location.FSPath(), path)
			if err != nil {
				return err
			}

			files = append(files, basenode.IFile{
				Path:      relPath,
				LocalPath: path,
			})
		}

		return nil
	})

	return files, err
}

func (s *ExtensionManagementService) getGalleryExtensionID(publisher, name string) string {
	return fmt.Sprintf("%s.%s", publisher, name)
}

func (s *ExtensionManagementService) isEngineValid(engine, version string) bool {
	// Simple semantic version validation
	// Parse the engine requirement (e.g., "^1.74.0")
	engine = strings.TrimSpace(engine)
	if engine == "" {
		return true
	}

	// Remove caret and tilde operators for simple comparison
	engine = strings.TrimPrefix(engine, "^")
	engine = strings.TrimPrefix(engine, "~")
	engine = strings.TrimPrefix(engine, ">=")
	engine = strings.TrimPrefix(engine, "<=")
	engine = strings.TrimPrefix(engine, ">")
	engine = strings.TrimPrefix(engine, "<")
	engine = strings.TrimPrefix(engine, "=")

	// Simple version comparison - just check if current version is greater or equal
	return basesemver.Gte(version, engine, nil)
}

func (s *ExtensionManagementService) addExtensionToProfile(
	extension *extensionmanagementcommon.ILocalExtension,
	metadata *extensionmanagementcommon.Metadata,
	profileLocation basecommon.URI,
) error {
	// Add extension to profile by updating profile metadata
	_, err := s.extensionsScanner.UpdateMetadata(extension, metadata, profileLocation)
	return err
}

// ExtensionsScanner handles extension scanning and management
type ExtensionsScanner struct {
	basecommon.Disposable

	beforeRemovingExtension         func(*extensionmanagementcommon.ILocalExtension) error
	fileService                     IFileService
	extensionsScannerService        IExtensionsScannerService
	extensionsProfileScannerService IExtensionsProfileScannerService
	uriIdentityService              IUriIdentityService
	logService                      ILogService

	obsoletedResource         basecommon.URI
	scanAllExtensionPromise   sync.Map
	scanUserExtensionsPromise sync.Map
}

type ExtensionKey struct {
	ID      string
	Version string
}

func NewExtensionsScanner(
	beforeRemovingExtension func(*extensionmanagementcommon.ILocalExtension) error,
	fileService IFileService,
	extensionsScannerService IExtensionsScannerService,
	extensionsProfileScannerService IExtensionsProfileScannerService,
	uriIdentityService IUriIdentityService,
	logService ILogService,
) *ExtensionsScanner {
	return &ExtensionsScanner{
		beforeRemovingExtension:         beforeRemovingExtension,
		fileService:                     fileService,
		extensionsScannerService:        extensionsScannerService,
		extensionsProfileScannerService: extensionsProfileScannerService,
		uriIdentityService:              uriIdentityService,
		logService:                      logService,
	}
}

func (scanner *ExtensionsScanner) ScanExtensions(
	extensionType *extensionscommon.ExtensionType,
	profileLocation basecommon.URI,
	productVersion extensionmanagementcommon.IProductVersion,
	language *string,
) ([]*extensionmanagementcommon.ILocalExtension, error) {

	scanner.logService.Trace("ExtensionsScanner#scanExtensions", profileLocation.ToString())

	// Implementation would scan extensions based on type and profile
	extensions, err := scanner.extensionsProfileScannerService.ScanProfileExtensions(profileLocation)
	if err != nil {
		return nil, err
	}

	var localExtensions []*extensionmanagementcommon.ILocalExtension
	for _, ext := range extensions {
		localExt := scanner.toLocalExtension(&ext)
		localExtensions = append(localExtensions, localExt)
	}

	return localExtensions, nil
}

func (scanner *ExtensionsScanner) ScanAllUserExtensions() ([]*extensionmanagementcommon.ILocalExtension, error) {
	scanner.logService.Trace("ExtensionsScanner#scanAllUserExtensions")

	// Scan user extensions from default location
	userExtensionsLocation := *basecommon.File(filepath.Join(os.Getenv("HOME"), ".vscode", "extensions"))
	extensions, err := scanner.extensionsScannerService.ScanUserExtensions(userExtensionsLocation)
	if err != nil {
		return nil, err
	}

	var localExtensions []*extensionmanagementcommon.ILocalExtension
	for _, ext := range extensions {
		localExt := scanner.toLocalExtension(&ext)
		localExtensions = append(localExtensions, localExt)
	}

	return localExtensions, nil
}

func (scanner *ExtensionsScanner) ScanUserExtensionAtLocation(location basecommon.URI) (*extensionmanagementcommon.ILocalExtension, error) {
	scanner.logService.Trace("ExtensionsScanner#scanUserExtensionAtLocation", location.ToString())

	// Read manifest from location
	manifestPath := filepath.Join(location.FSPath(), "package.json")
	data, err := os.ReadFile(manifestPath)
	if err != nil {
		return nil, err
	}

	var manifest extensionscommon.IExtensionManifest
	if err := json.Unmarshal(data, &manifest); err != nil {
		return nil, err
	}

	// Create extension from manifest
	extension := &extensionscommon.IExtension{
		Type:           extensionscommon.ExtensionTypeUser,
		IsBuiltin:      false,
		Identifier:     extensionscommon.IExtensionIdentifier{ID: fmt.Sprintf("%s.%s", manifest.Publisher, manifest.Name)},
		Manifest:       manifest,
		Location:       location,
		TargetPlatform: extensionscommon.GetCurrentTargetPlatform(),
		IsValid:        true,
		Validations:    []extensionscommon.ValidationMessage{},
		PreRelease:     false,
	}

	return scanner.toLocalExtension(extension), nil
}

func (scanner *ExtensionsScanner) ExtractUserExtension(
	extensionKey ExtensionKey,
	zipPath string,
	removeIfExists bool,
	token basecommon.CancellationToken,
) (*extensionmanagementcommon.ILocalExtension, error) {

	scanner.logService.Trace("ExtensionsScanner#extractUserExtension", extensionKey.ID)

	// Determine extraction path
	userExtensionsDir := filepath.Join(os.Getenv("HOME"), ".vscode", "extensions")
	extractPath := filepath.Join(userExtensionsDir, fmt.Sprintf("%s-%s", extensionKey.ID, extensionKey.Version))

	// Remove existing if requested
	if removeIfExists {
		if err := os.RemoveAll(extractPath); err != nil && !os.IsNotExist(err) {
			return nil, err
		}
	}

	// Extract extension
	err := basenode.Extract(zipPath, extractPath, basenode.IExtractOptions{}, token)
	if err != nil {
		return nil, err
	}

	// Scan extracted extension
	extractedLocation := *basecommon.File(extractPath)
	return scanner.ScanUserExtensionAtLocation(extractedLocation)
}

func (scanner *ExtensionsScanner) UpdateMetadata(
	local *extensionmanagementcommon.ILocalExtension,
	metadata *extensionmanagementcommon.Metadata,
	profileLocation basecommon.URI,
) (*extensionmanagementcommon.ILocalExtension, error) {

	scanner.logService.Trace("ExtensionsScanner#updateMetadata", local.Identifier.ID)

	// Update extension metadata in profile
	// This would typically update a metadata file associated with the profile

	// For now, just return the updated extension
	updatedLocal := *local
	if metadata != nil {
		updatedLocal.Source = metadata.Source
		updatedLocal.InstalledTimestamp = metadata.InstalledTimestamp
		updatedLocal.IsPreReleaseVersion = metadata.IsPreReleaseVersion
		updatedLocal.HasPreReleaseVersion = metadata.HasPreReleaseVersion
		updatedLocal.Updated = metadata.Updated
		updatedLocal.PreRelease = metadata.PreRelease
		updatedLocal.Private = metadata.Private
		updatedLocal.IsApplicationScoped = metadata.IsApplicationScoped
		updatedLocal.IsMachineScoped = metadata.IsMachineScoped
		updatedLocal.Pinned = metadata.Pinned
		updatedLocal.Size = metadata.Size
	}

	return &updatedLocal, nil
}

func (scanner *ExtensionsScanner) DeleteExtension(extension *extensionmanagementcommon.ILocalExtension, extensionType string) error {
	scanner.logService.Trace("ExtensionsScanner#deleteExtension", extension.Identifier.ID)

	// Call lifecycle hook
	if err := scanner.beforeRemovingExtension(extension); err != nil {
		return err
	}

	// Delete extension directory
	return os.RemoveAll(extension.Location.FSPath())
}

func (scanner *ExtensionsScanner) toLocalExtension(extension *extensionscommon.IExtension) *extensionmanagementcommon.ILocalExtension {
	now := time.Now()
	return &extensionmanagementcommon.ILocalExtension{
		IExtension:           *extension,
		IsWorkspaceScoped:    false,
		IsMachineScoped:      false,
		IsApplicationScoped:  false,
		PublisherID:          nil,
		InstalledTimestamp:   &now,
		IsPreReleaseVersion:  false,
		HasPreReleaseVersion: false,
		Private:              false,
		PreRelease:           extension.PreRelease,
		Updated:              false,
		Pinned:               false,
		Source:               extensionmanagementcommon.InstallSourceGallery,
		Size:                 0,
	}
}
