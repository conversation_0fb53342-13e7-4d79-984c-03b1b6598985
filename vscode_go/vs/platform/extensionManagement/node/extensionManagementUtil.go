/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"encoding/json"

	basenode "github.com/yudaprama/kawai-agent/vscode_go/vs/base/node"
	"github.com/yudaprama/kawai-agent/vscode_go/vs/nls"
	extensionmanagementcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/extensionManagement/common"
	extensionscommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/extensions/common"
)

// FromExtractError converts an extract error to an ExtensionManagementError
func FromExtractError(err error) *extensionmanagementcommon.ExtensionManagementError {
	errorCode := extensionmanagementcommon.ExtensionManagementErrorCodeExtract

	if extractErr, ok := err.(*basenode.ExtractError); ok {
		switch extractErr.Type {
		case basenode.CorruptZip:
			errorCode = extensionmanagementcommon.ExtensionManagementErrorCodeCorruptZip
		case basenode.Incomplete:
			errorCode = extensionmanagementcommon.ExtensionManagementErrorCodeIncompleteZip
		}
	}

	return extensionmanagementcommon.ToExtensionManagementError(err, errorCode)
}

// GetManifest extracts and returns the manifest from a VSIX file
func GetManifest(vsixPath string) (*extensionscommon.IExtensionManifest, error) {
	// Extract package.json from the VSIX file
	data, err := basenode.Buffer(vsixPath, "extension/package.json")
	if err != nil {
		return nil, FromExtractError(err)
	}

	// Parse the JSON manifest
	var manifest extensionscommon.IExtensionManifest
	if err := json.Unmarshal(data, &manifest); err != nil {
		return nil, extensionmanagementcommon.NewExtensionManagementError(
			nls.Localize("invalidManifest", "VSIX invalid: package.json is not a JSON file."),
			extensionmanagementcommon.ExtensionManagementErrorCodeInvalid,
		)
	}

	return &manifest, nil
}
