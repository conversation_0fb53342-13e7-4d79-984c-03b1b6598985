/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	environmentcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/environment/common"
	extensionmanagementcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/extensionManagement/common"
	extensionrecommendationscommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/extensionRecommendations/common"
	filescommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/common"
	nativecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/native/common"
	productcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/product/common"
	storagecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/storage/common"
	telemetrycommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/telemetry/common"
)

// ExtensionTipsService extends AbstractNativeExtensionTipsService for node environment
type ExtensionTipsService struct {
	*extensionmanagementcommon.AbstractNativeExtensionTipsService
}

// NewExtensionTipsService creates a new ExtensionTipsService instance
func NewExtensionTipsService(
	environmentService environmentcommon.INativeEnvironmentService,
	telemetryService telemetrycommon.ITelemetryService,
	extensionManagementService extensionmanagementcommon.IExtensionManagementService,
	storageService storagecommon.IStorageService,
	nativeHostService nativecommon.INativeHostService,
	extensionRecommendationNotificationService extensionrecommendationscommon.IExtensionRecommendationNotificationService,
	fileService filescommon.IFileService,
	productService productcommon.IProductService,
) *ExtensionTipsService {
	
	userHome := environmentService.GetUserHome()
	
	abstractService := extensionmanagementcommon.NewAbstractNativeExtensionTipsService(
		userHome,
		nativeHostService,
		telemetryService,
		extensionManagementService,
		storageService,
		extensionRecommendationNotificationService,
		fileService,
		productService,
	)
	
	return &ExtensionTipsService{
		AbstractNativeExtensionTipsService: abstractService,
	}
}
