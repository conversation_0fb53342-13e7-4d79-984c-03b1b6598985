/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	environmentcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/environment/common"
	extensionmanagementcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/extensionManagement/common"
	filescommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/common"
	logcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
	uriidentitycommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/uriIdentity/common"
	userdataprofilecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/userDataProfile/common"
)

// ExtensionsProfileScannerService extends AbstractExtensionsProfileScannerService for node environment
type ExtensionsProfileScannerService struct {
	*extensionmanagementcommon.AbstractExtensionsProfileScannerService
}

// NewExtensionsProfileScannerService creates a new ExtensionsProfileScannerService instance
func NewExtensionsProfileScannerService(
	environmentService environmentcommon.INativeEnvironmentService,
	fileService filescommon.IFileService,
	userDataProfilesService userdataprofilecommon.IUserDataProfilesService,
	uriIdentityService uriidentitycommon.IUriIdentityService,
	logService logcommon.ILogService,
) *ExtensionsProfileScannerService {
	
	extensionsPath := environmentService.GetExtensionsPath()
	extensionsLocation := basecommon.FileURI(extensionsPath)
	
	abstractService := extensionmanagementcommon.NewAbstractExtensionsProfileScannerService(
		extensionsLocation,
		fileService,
		userDataProfilesService,
		uriIdentityService,
		logService,
	)
	
	return &ExtensionsProfileScannerService{
		AbstractExtensionsProfileScannerService: abstractService,
	}
}
