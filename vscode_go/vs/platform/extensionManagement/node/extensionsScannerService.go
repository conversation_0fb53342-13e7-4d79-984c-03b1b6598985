/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	environmentcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/environment/common"
	extensionmanagementcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/extensionManagement/common"
	filescommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/common"
	instantiationcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/instantiation/common"
	logcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
	productcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/product/common"
	uriidentitycommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/uriIdentity/common"
	userdataprofilecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/userDataProfile/common"
)

// ExtensionsScannerService extends BaseExtensionsScannerService and implements IExtensionsScannerService
type ExtensionsScannerService struct {
	*extensionmanagementcommon.BaseExtensionsScannerService
}

// NewExtensionsScannerService creates a new ExtensionsScannerService instance
func NewExtensionsScannerService(
	userDataProfilesService userdataprofilecommon.IUserDataProfilesService,
	extensionsProfileScannerService extensionmanagementcommon.IExtensionsProfileScannerService,
	fileService filescommon.IFileService,
	logService logcommon.ILogService,
	environmentService environmentcommon.INativeEnvironmentService,
	productService productcommon.IProductService,
	uriIdentityService uriidentitycommon.IUriIdentityService,
	instantiationService instantiationcommon.IInstantiationService,
) *ExtensionsScannerService {

	builtinExtensionsPath := environmentService.BuiltinExtensionsPath()
	builtinExtensionsLocation := *basecommon.FileURI(builtinExtensionsPath)

	extensionsPath := environmentService.ExtensionsPath()
	extensionsLocation := *basecommon.FileURI(extensionsPath)

	baseService := extensionmanagementcommon.NewBaseExtensionsScannerService(
		builtinExtensionsLocation,
		extensionsLocation,
	)

	return &ExtensionsScannerService{
		BaseExtensionsScannerService: baseService,
	}
}
