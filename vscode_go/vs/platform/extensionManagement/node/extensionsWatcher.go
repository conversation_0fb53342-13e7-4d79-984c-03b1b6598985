/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"fmt"
	"sync"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	extensionmanagementcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/extensionManagement/common"
	extensionscommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/extensions/common"
	filescommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/common"
	logcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
	uriidentitycommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/uriIdentity/common"
	userdataprofilecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/userDataProfile/common"
)

// DidChangeProfileExtensionsEvent represents changes to profile extensions
type DidChangeProfileExtensionsEvent struct {
	Added   *ProfileExtensionsChange `json:"added,omitempty"`
	Removed *ProfileExtensionsChange `json:"removed,omitempty"`
}

// ProfileExtensionsChange represents a change in profile extensions
type ProfileExtensionsChange struct {
	Extensions      []extensionscommon.IExtensionIdentifier `json:"extensions"`
	ProfileLocation basecommon.URI                          `json:"profileLocation"`
}

// ExtensionsWatcher watches for changes in extension profiles
type ExtensionsWatcher struct {
	*basecommon.Disposable

	// Events
	onDidChangeExtensionsByAnotherSource *basecommon.Emitter[DidChangeProfileExtensionsEvent]

	// Services
	extensionManagementService      INativeServerExtensionManagementService
	extensionsScannerService        extensionmanagementcommon.IExtensionsScannerService
	userDataProfilesService         userdataprofilecommon.IUserDataProfilesService
	extensionsProfileScannerService extensionmanagementcommon.IExtensionsProfileScannerService
	uriIdentityService              uriidentitycommon.IUriIdentityService
	fileService                     filescommon.IFileService
	logService                      logcommon.ILogService

	// State
	allExtensions                     map[string]*basecommon.ResourceSet
	extensionsProfileWatchDisposables *basecommon.DisposableMap[string, basecommon.IDisposable]
	mu                                sync.RWMutex
}

// INativeServerExtensionManagementService interface for extension management
type INativeServerExtensionManagementService interface {
	ScanInstalledExtensionAtLocation(location basecommon.URI) (*extensionscommon.IExtension, error)
	ScanAllUserInstalledExtensions() ([]*extensionscommon.IExtension, error)
	DeleteExtensions(extensions ...*extensionscommon.IExtension) error
}

// NewExtensionsWatcher creates a new ExtensionsWatcher instance
func NewExtensionsWatcher(
	extensionManagementService INativeServerExtensionManagementService,
	extensionsScannerService extensionmanagementcommon.IExtensionsScannerService,
	userDataProfilesService userdataprofilecommon.IUserDataProfilesService,
	extensionsProfileScannerService extensionmanagementcommon.IExtensionsProfileScannerService,
	uriIdentityService uriidentitycommon.IUriIdentityService,
	fileService filescommon.IFileService,
	logService logcommon.ILogService,
) *ExtensionsWatcher {

	watcher := &ExtensionsWatcher{
		Disposable:                           basecommon.NewDisposable(),
		onDidChangeExtensionsByAnotherSource: basecommon.NewEmitter[DidChangeProfileExtensionsEvent](),
		extensionManagementService:           extensionManagementService,
		extensionsScannerService:             extensionsScannerService,
		userDataProfilesService:              userDataProfilesService,
		extensionsProfileScannerService:      extensionsProfileScannerService,
		uriIdentityService:                   uriIdentityService,
		fileService:                          fileService,
		logService:                           logService,
		allExtensions:                        make(map[string]*basecommon.ResourceSet),
		extensionsProfileWatchDisposables:    basecommon.NewDisposableMap[string, basecommon.IDisposable](),
	}

	// Register disposables
	watcher.Register(watcher.onDidChangeExtensionsByAnotherSource)
	watcher.Register(watcher.extensionsProfileWatchDisposables)

	// Initialize asynchronously
	go func() {
		if err := watcher.initialize(); err != nil {
			logService.Error("Error while initializing Extensions Watcher", basecommon.GetErrorMessage(err))
		}
	}()

	return watcher
}

// OnDidChangeExtensionsByAnotherSource returns the event for extension changes
func (w *ExtensionsWatcher) OnDidChangeExtensionsByAnotherSource() basecommon.Event[DidChangeProfileExtensionsEvent] {
	return w.onDidChangeExtensionsByAnotherSource
}

// initialize sets up the watcher
func (w *ExtensionsWatcher) initialize() error {
	if err := w.extensionsScannerService.InitializeDefaultProfileExtensions(); err != nil {
		return err
	}

	profiles := w.userDataProfilesService.Profiles()
	if err := w.onDidChangeProfiles(profiles); err != nil {
		return err
	}

	w.registerListeners()

	return w.deleteExtensionsNotInProfiles(nil)
}

// registerListeners sets up event listeners
func (w *ExtensionsWatcher) registerListeners() {
	// Listen to profile changes
	w.Register(w.userDataProfilesService.OnDidChangeProfiles().Subscribe(func(e *userdataprofilecommon.DidChangeProfilesEvent) {
		if err := w.onDidChangeProfiles(e.Added); err != nil {
			w.logService.Error("Error handling profile changes", basecommon.GetErrorMessage(err))
		}
	}))

	// Listen to extension profile changes
	w.Register(w.extensionsProfileScannerService.OnAddExtensions().Subscribe(func(e extensionmanagementcommon.ProfileExtensionsEvent) {
		if err := w.onAddExtensions(e); err != nil {
			w.logService.Error("Error handling add extensions", basecommon.GetErrorMessage(err))
		}
	}))

	w.Register(w.extensionsProfileScannerService.OnDidAddExtensions().Subscribe(func(e extensionmanagementcommon.DidAddProfileExtensionsEvent) {
		if err := w.onDidAddExtensions(e); err != nil {
			w.logService.Error("Error handling did add extensions", basecommon.GetErrorMessage(err))
		}
	}))

	w.Register(w.extensionsProfileScannerService.OnRemoveExtensions().Subscribe(func(e extensionmanagementcommon.ProfileExtensionsEvent) {
		if err := w.onRemoveExtensions(e); err != nil {
			w.logService.Error("Error handling remove extensions", basecommon.GetErrorMessage(err))
		}
	}))

	w.Register(w.extensionsProfileScannerService.OnDidRemoveExtensions().Subscribe(func(e extensionmanagementcommon.DidRemoveProfileExtensionsEvent) {
		if err := w.onDidRemoveExtensions(e); err != nil {
			w.logService.Error("Error handling did remove extensions", basecommon.GetErrorMessage(err))
		}
	}))

	// Listen to file changes
	w.Register(w.fileService.OnDidFilesChange().Subscribe(func(e *filescommon.FileChangesEvent) {
		w.onDidFilesChange(*e)
	}))
}

// onDidChangeProfiles handles profile changes
func (w *ExtensionsWatcher) onDidChangeProfiles(added []*userdataprofilecommon.IUserDataProfile) error {
	if len(added) == 0 {
		return nil
	}

	for _, profile := range added {
		// Set up file watching for the profile
		dirname := w.uriIdentityService.ExtUri().Dirname(profile.ExtensionsResource)
		watchDisposable := basecommon.CombinedDisposable(
			w.fileService.Watch(dirname, nil),
			w.fileService.Watch(profile.ExtensionsResource, nil),
		)

		w.extensionsProfileWatchDisposables.Set(profile.ID, watchDisposable)

		// Populate extensions from this profile
		if err := w.populateExtensionsFromProfile(*profile.ExtensionsResource); err != nil {
			return err
		}
	}

	return nil
}

// getKey creates a unique key for an extension
func (w *ExtensionsWatcher) getKey(identifier extensionscommon.IExtensionIdentifier, version string) string {
	return fmt.Sprintf("%s@%s", extensionscommon.ExtensionIdentifierToKey(identifier.ID), version)
}

// fromKey parses a key back to identifier and version
func (w *ExtensionsWatcher) fromKey(key string) *struct {
	Identifier extensionscommon.IExtensionIdentifier
	Version    string
} {
	id, version := extensionmanagementcommon.GetIdAndVersion(key)
	if version == nil {
		return nil
	}

	return &struct {
		Identifier extensionscommon.IExtensionIdentifier
		Version    string
	}{
		Identifier: extensionscommon.NewExtensionIdentifier(id),
		Version:    *version,
	}
}

// onAddExtensions handles adding extensions to profiles
func (w *ExtensionsWatcher) onAddExtensions(e extensionmanagementcommon.ProfileExtensionsEvent) error {
	for _, extension := range e.Extensions {
		key := w.getKey(extension.Identifier, extension.Version)
		w.addExtensionWithKey(key, e.ProfileLocation)
	}
	return nil
}

// onDidAddExtensions handles completion of adding extensions
func (w *ExtensionsWatcher) onDidAddExtensions(e extensionmanagementcommon.DidAddProfileExtensionsEvent) error {
	for _, extension := range e.Extensions {
		key := w.getKey(extension.Identifier, extension.Version)
		if e.Error != nil {
			w.removeExtensionWithKey(key, e.ProfileLocation)
		} else {
			w.addExtensionWithKey(key, e.ProfileLocation)
		}
	}
	return nil
}

// onRemoveExtensions handles removing extensions from profiles
func (w *ExtensionsWatcher) onRemoveExtensions(e extensionmanagementcommon.ProfileExtensionsEvent) error {
	for _, extension := range e.Extensions {
		key := w.getKey(extension.Identifier, extension.Version)
		w.removeExtensionWithKey(key, e.ProfileLocation)
	}
	return nil
}

// onDidRemoveExtensions handles completion of removing extensions
func (w *ExtensionsWatcher) onDidRemoveExtensions(e extensionmanagementcommon.DidRemoveProfileExtensionsEvent) error {
	var extensionsToDelete []*extensionscommon.IExtension

	for _, extension := range e.Extensions {
		key := w.getKey(extension.Identifier, extension.Version)
		if e.Error != nil {
			w.addExtensionWithKey(key, e.ProfileLocation)
		} else {
			w.removeExtensionWithKey(key, e.ProfileLocation)

			w.mu.RLock()
			_, exists := w.allExtensions[key]
			w.mu.RUnlock()

			if !exists {
				w.logService.Debug("Extension is removed from all profiles", extension.Identifier.ID, extension.Version)

				result, err := w.extensionManagementService.ScanInstalledExtensionAtLocation(extension.Location)
				if err != nil {
					w.logService.Error("Error scanning extension", basecommon.GetErrorMessage(err))
				} else if result != nil {
					extensionsToDelete = append(extensionsToDelete, result)
				} else {
					w.logService.Info("Extension not found at the location", extension.Location.ToString())
				}
			}
		}
	}

	if len(extensionsToDelete) > 0 {
		return w.deleteExtensionsNotInProfiles(extensionsToDelete)
	}

	return nil
}

// onDidFilesChange handles file system changes
func (w *ExtensionsWatcher) onDidFilesChange(e filescommon.FileChangesEvent) {
	profiles := w.userDataProfilesService.Profiles()
	for _, profile := range profiles {
		if e.Contains(profile.ExtensionsResource, filescommon.FileChangeTypeUpdated, filescommon.FileChangeTypeAdded) {
			go func(profileLocation basecommon.URI) {
				if err := w.onDidExtensionsProfileChange(profileLocation); err != nil {
					w.logService.Error("Error handling profile change", basecommon.GetErrorMessage(err))
				}
			}(*profile.ExtensionsResource)
		}
	}
}

// onDidExtensionsProfileChange handles changes to extension profiles
func (w *ExtensionsWatcher) onDidExtensionsProfileChange(profileLocation basecommon.URI) error {
	var added, removed []extensionscommon.IExtensionIdentifier

	extensions, err := w.extensionsProfileScannerService.ScanProfileExtensions(profileLocation, nil)
	if err != nil {
		return err
	}

	extensionKeys := make(map[string]bool)
	cached := make(map[string]bool)

	// Get currently cached extensions for this profile
	w.mu.RLock()
	for key, profiles := range w.allExtensions {
		if profiles.Has(&profileLocation) {
			cached[key] = true
		}
	}
	w.mu.RUnlock()

	// Process current extensions
	for _, extension := range extensions {
		key := w.getKey(extension.Identifier, extension.Version)
		extensionKeys[key] = true

		if !cached[key] {
			added = append(added, extension.Identifier)
			w.addExtensionWithKey(key, profileLocation)
		}
	}

	// Find removed extensions
	for key := range cached {
		if !extensionKeys[key] {
			if extension := w.fromKey(key); extension != nil {
				removed = append(removed, extension.Identifier)
				w.removeExtensionWithKey(key, profileLocation)
			}
		}
	}

	// Fire change event if there are changes
	if len(added) > 0 || len(removed) > 0 {
		event := DidChangeProfileExtensionsEvent{}
		if len(added) > 0 {
			event.Added = &ProfileExtensionsChange{
				Extensions:      added,
				ProfileLocation: profileLocation,
			}
		}
		if len(removed) > 0 {
			event.Removed = &ProfileExtensionsChange{
				Extensions:      removed,
				ProfileLocation: profileLocation,
			}
		}
		w.onDidChangeExtensionsByAnotherSource.Fire(event)
	}

	return nil
}

// populateExtensionsFromProfile loads extensions from a profile
func (w *ExtensionsWatcher) populateExtensionsFromProfile(extensionsProfileLocation basecommon.URI) error {
	extensions, err := w.extensionsProfileScannerService.ScanProfileExtensions(extensionsProfileLocation, nil)
	if err != nil {
		return err
	}

	for _, extension := range extensions {
		key := w.getKey(extension.Identifier, extension.Version)
		w.addExtensionWithKey(key, extensionsProfileLocation)
	}

	return nil
}

// deleteExtensionsNotInProfiles removes extensions that are not in any profile
func (w *ExtensionsWatcher) deleteExtensionsNotInProfiles(toDelete []*extensionscommon.IExtension) error {
	if toDelete == nil {
		installed, err := w.extensionManagementService.ScanAllUserInstalledExtensions()
		if err != nil {
			return err
		}

		w.mu.RLock()
		for _, installedExtension := range installed {
			key := w.getKey(installedExtension.Identifier, installedExtension.Manifest.Version)
			if _, exists := w.allExtensions[key]; !exists {
				toDelete = append(toDelete, installedExtension)
			}
		}
		w.mu.RUnlock()
	}

	if len(toDelete) > 0 {
		return w.extensionManagementService.DeleteExtensions(toDelete...)
	}

	return nil
}

// addExtensionWithKey adds an extension to the tracking map
func (w *ExtensionsWatcher) addExtensionWithKey(key string, extensionsProfileLocation basecommon.URI) {
	w.mu.Lock()
	defer w.mu.Unlock()

	profiles, exists := w.allExtensions[key]
	if !exists {
		getKey := func(uri *basecommon.URI) string {
			return w.uriIdentityService.ExtUri().GetComparisonKey(uri)
		}
		profiles = basecommon.NewResourceSet(getKey)
		w.allExtensions[key] = profiles
	}
	profiles.Add(&extensionsProfileLocation)
}

// removeExtensionWithKey removes an extension from the tracking map
func (w *ExtensionsWatcher) removeExtensionWithKey(key string, profileLocation basecommon.URI) {
	w.mu.Lock()
	defer w.mu.Unlock()

	profiles, exists := w.allExtensions[key]
	if exists {
		profiles.Delete(&profileLocation)
		if profiles.Size() == 0 {
			delete(w.allExtensions, key)
		}
	}
}
