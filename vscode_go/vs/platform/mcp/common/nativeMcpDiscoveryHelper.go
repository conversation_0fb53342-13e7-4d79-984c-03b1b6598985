/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
)

// INativeMcpDiscoveryData represents discovery data for MCP
type INativeMcpDiscoveryData struct {
	Platform   basecommon.Platform `json:"platform"`
	Homedir    *basecommon.URI     `json:"homedir"`
	WinAppData *basecommon.URI     `json:"winAppData,omitempty"`
	XdgHome    *basecommon.URI     `json:"xdgHome,omitempty"`
}

// INativeMcpDiscoveryHelperService provides MCP discovery helper functionality
type INativeMcpDiscoveryHelperService interface {
	// Service brand for type safety
	ServiceBrand() interface{}

	// Load discovery data
	Load() (*INativeMcpDiscoveryData, error)
}
