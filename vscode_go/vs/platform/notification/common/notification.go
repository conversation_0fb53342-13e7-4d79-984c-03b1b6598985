/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	instantiationcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/instantiation/common"
)

// Severity represents notification severity levels
type Severity int

const (
	SeverityInfo Severity = iota
	SeverityWarning
	SeverityError
)

// NotificationMessage represents a notification message (string or error)
type NotificationMessage interface{}

// INotificationProgress represents notification progress
type INotificationProgress interface {
	Infinite()
	Done()
	Total(value int)
	Worked(value int)
}

// INotificationHandle represents a handle to a notification
type INotificationHandle interface {
	// Progress returns the progress interface
	Progress() INotificationProgress
	// OnDidClose returns an event fired when the notification is closed
	OnDidClose() basecommon.Event[interface{}]
	// OnDidChangeVisibility returns an event fired when visibility changes
	OnDidChangeVisibility() basecommon.Event[bool]
	// UpdateSeverity updates the notification severity
	UpdateSeverity(severity Severity)
	// UpdateMessage updates the notification message
	UpdateMessage(message NotificationMessage)
	// UpdateActions updates the notification actions
	UpdateActions(actions *INotificationActions)
	// Close closes the notification
	Close()
}

// INotificationActions represents notification actions
type INotificationActions struct {
	Primary   []INotificationAction `json:"primary,omitempty"`
	Secondary []INotificationAction `json:"secondary,omitempty"`
}

// INotificationAction represents a notification action
type INotificationAction interface {
	GetID() string
	GetLabel() string
	GetTooltip() string
	GetClass() string
	GetEnabled() bool
	GetChecked() bool
	Run() error
}

// INotification represents a notification
type INotification struct {
	Severity Severity                `json:"severity"`
	Message  NotificationMessage     `json:"message"`
	Source   *INotificationSource    `json:"source,omitempty"`
	Actions  *INotificationActions   `json:"actions,omitempty"`
	Silent   bool                    `json:"silent,omitempty"`
	Sticky   bool                    `json:"sticky,omitempty"`
	Priority int                     `json:"priority,omitempty"`
}

// INotificationSource represents a notification source
type INotificationSource struct {
	ID    string `json:"id"`
	Label string `json:"label"`
}

// IPromptChoice represents a prompt choice
type IPromptChoice struct {
	Label     string      `json:"label"`
	IsSecondary bool      `json:"isSecondary,omitempty"`
	KeepOpen  bool        `json:"keepOpen,omitempty"`
	Run       func() error `json:"-"`
}

// IPromptOptions represents prompt options
type IPromptOptions struct {
	Modal       bool                    `json:"modal,omitempty"`
	CancelButton *IPromptChoice         `json:"cancelButton,omitempty"`
	Detail      string                  `json:"detail,omitempty"`
	Checkbox    *IPromptCheckbox        `json:"checkbox,omitempty"`
	Custom      *IPromptCustomOptions   `json:"custom,omitempty"`
}

// IPromptCheckbox represents a prompt checkbox
type IPromptCheckbox struct {
	Label   string `json:"label"`
	Checked bool   `json:"checked"`
}

// IPromptCustomOptions represents custom prompt options
type IPromptCustomOptions struct {
	Icon      string `json:"icon,omitempty"`
	MarkdownDetails []string `json:"markdownDetails,omitempty"`
}

// IStatusMessageOptions represents status message options
type IStatusMessageOptions struct {
	HideAfter int    `json:"hideAfter,omitempty"`
	ShowProgress bool `json:"showProgress,omitempty"`
}

// IStatusHandle represents a handle to a status message
type IStatusHandle interface {
	Dispose()
}

// INotificationService represents the notification service interface
type INotificationService interface {
	// ServiceBrand for type safety
	ServiceBrand() interface{}

	// OnDidChangeFilter returns an event fired when the notification filter changes
	OnDidChangeFilter() basecommon.Event[interface{}]

	// Info shows an info notification
	Info(message string) INotificationHandle

	// Warn shows a warning notification
	Warn(message string) INotificationHandle

	// Error shows an error notification
	Error(error interface{}) INotificationHandle // Can be string or error

	// Notify shows a notification
	Notify(notification *INotification) INotificationHandle

	// Prompt shows a prompt notification
	Prompt(severity Severity, message string, choices []*IPromptChoice, options *IPromptOptions) (*IPromptChoice, error)

	// Status shows a status message
	Status(message NotificationMessage, options *IStatusMessageOptions) IStatusHandle
}

// NoOpProgress implements INotificationProgress with no-op methods
type NoOpProgress struct{}

func (p *NoOpProgress) Infinite() {}
func (p *NoOpProgress) Done() {}
func (p *NoOpProgress) Total(value int) {}
func (p *NoOpProgress) Worked(value int) {}

// NoOpNotification implements INotificationHandle with no-op methods
type NoOpNotification struct {
	progress *NoOpProgress
	onDidClose *basecommon.Emitter[interface{}]
	onDidChangeVisibility *basecommon.Emitter[bool]
}

func NewNoOpNotification() *NoOpNotification {
	return &NoOpNotification{
		progress: &NoOpProgress{},
		onDidClose: basecommon.NewEmitter[interface{}](),
		onDidChangeVisibility: basecommon.NewEmitter[bool](),
	}
}

func (n *NoOpNotification) Progress() INotificationProgress {
	return n.progress
}

func (n *NoOpNotification) OnDidClose() basecommon.Event[interface{}] {
	return n.onDidClose.Event()
}

func (n *NoOpNotification) OnDidChangeVisibility() basecommon.Event[bool] {
	return n.onDidChangeVisibility.Event()
}

func (n *NoOpNotification) UpdateSeverity(severity Severity) {}
func (n *NoOpNotification) UpdateMessage(message NotificationMessage) {}
func (n *NoOpNotification) UpdateActions(actions *INotificationActions) {}
func (n *NoOpNotification) Close() {}

// NoOpStatusHandle implements IStatusHandle with no-op methods
type NoOpStatusHandle struct{}

func (h *NoOpStatusHandle) Dispose() {}

// TestNotificationService provides a test implementation of INotificationService
type TestNotificationService struct {
	onDidChangeFilter *basecommon.Emitter[interface{}]
}

func NewTestNotificationService() *TestNotificationService {
	return &TestNotificationService{
		onDidChangeFilter: basecommon.NewEmitter[interface{}](),
	}
}

func (s *TestNotificationService) ServiceBrand() interface{} {
	return "notificationService"
}

func (s *TestNotificationService) OnDidChangeFilter() basecommon.Event[interface{}] {
	return s.onDidChangeFilter.Event()
}

func (s *TestNotificationService) Info(message string) INotificationHandle {
	return s.Notify(&INotification{Severity: SeverityInfo, Message: message})
}

func (s *TestNotificationService) Warn(message string) INotificationHandle {
	return s.Notify(&INotification{Severity: SeverityWarning, Message: message})
}

func (s *TestNotificationService) Error(error interface{}) INotificationHandle {
	return s.Notify(&INotification{Severity: SeverityError, Message: error})
}

func (s *TestNotificationService) Notify(notification *INotification) INotificationHandle {
	return NewNoOpNotification()
}

func (s *TestNotificationService) Prompt(severity Severity, message string, choices []*IPromptChoice, options *IPromptOptions) (*IPromptChoice, error) {
	if len(choices) > 0 {
		return choices[0], nil
	}
	return nil, nil
}

func (s *TestNotificationService) Status(message NotificationMessage, options *IStatusMessageOptions) IStatusHandle {
	return &NoOpStatusHandle{}
}

// Service identifier for dependency injection
var INotificationServiceID = instantiationcommon.CreateDecorator[INotificationService]("notificationService")
