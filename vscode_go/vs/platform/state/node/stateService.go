/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"encoding/json"
	"fmt"
	"sync"
	"time"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	environmentcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/environment/common"
	filescommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/common"
	logcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
)

// SaveStrategy represents the strategy for saving state
type SaveStrategy int

const (
	SaveStrategyImmediate SaveStrategy = iota
	SaveStrategyDelayed
)

// StorageDatabase represents the in-memory storage database
type StorageDatabase map[string]interface{}

// FileStorage provides file-based storage for state
type FileStorage struct {
	*basecommon.DisposableStore

	storage                  StorageDatabase
	lastSavedStorageContents string
	storagePath              *basecommon.URI
	saveStrategy             SaveStrategy
	logService               logcommon.ILogService
	fileService              filescommon.IFileService
	flushDelayer             *ThrottledDelayer
	initializing             chan error
	closing                  chan error
	mutex                    sync.RWMutex
	initOnce                 sync.Once
	closeOnce                sync.Once
}

// ThrottledDelayer provides throttled execution of functions
type ThrottledDelayer struct {
	delay    time.Duration
	timer    *time.Timer
	pending  chan func() error
	mutex    sync.Mutex
	disposed bool
}

// NewThrottledDelayer creates a new throttled delayer
func NewThrottledDelayer(delay time.Duration) *ThrottledDelayer {
	return &ThrottledDelayer{
		delay:   delay,
		pending: make(chan func() error, 1),
	}
}

// Trigger triggers the execution of a function with throttling
func (td *ThrottledDelayer) Trigger(fn func() error) error {
	td.mutex.Lock()
	defer td.mutex.Unlock()

	if td.disposed {
		return fmt.Errorf("delayer is disposed")
	}

	// Cancel existing timer
	if td.timer != nil {
		td.timer.Stop()
	}

	// Set the pending function
	select {
	case td.pending <- fn:
	default:
		// Replace existing pending function
		<-td.pending
		td.pending <- fn
	}

	// Start new timer
	td.timer = time.AfterFunc(td.delay, func() {
		select {
		case fn := <-td.pending:
			fn()
		default:
		}
	})

	return nil
}

// Dispose disposes the throttled delayer
func (td *ThrottledDelayer) Dispose() {
	td.mutex.Lock()
	defer td.mutex.Unlock()

	if td.disposed {
		return
	}

	td.disposed = true
	if td.timer != nil {
		td.timer.Stop()
	}
	close(td.pending)
}

// NewFileStorage creates a new file storage
func NewFileStorage(
	storagePath *basecommon.URI,
	saveStrategy SaveStrategy,
	logService logcommon.ILogService,
	fileService filescommon.IFileService,
) *FileStorage {
	delay := time.Duration(0)
	if saveStrategy == SaveStrategyDelayed {
		delay = 100 * time.Millisecond
	}

	fs := &FileStorage{
		DisposableStore: basecommon.NewDisposableStore(),
		storage:         make(StorageDatabase),
		storagePath:     storagePath,
		saveStrategy:    saveStrategy,
		logService:      logService,
		fileService:     fileService,
		flushDelayer:    NewThrottledDelayer(delay),
		initializing:    make(chan error, 1),
		closing:         make(chan error, 1),
	}

	fs.Register(basecommon.ToDisposable(func() {
		fs.flushDelayer.Dispose()
	}))

	return fs
}

// Init initializes the file storage
func (fs *FileStorage) Init() error {
	fs.initOnce.Do(func() {
		go func() {
			fs.initializing <- fs.doInit()
		}()
	})

	return <-fs.initializing
}

// doInit performs the actual initialization
func (fs *FileStorage) doInit() error {
	content, err := fs.fileService.ReadFile(fs.storagePath, nil, basecommon.CancellationTokenNone)
	if err != nil {
		// Check if it's a file not found error
		if filescommon.ToFileOperationResult(err) != filescommon.FileOperationResultFileNotFound {
			fs.logService.Error("Failed to read state file", err)
		}
		return nil // Not an error if file doesn't exist
	}

	fs.lastSavedStorageContents = content.Value.ToString()
	if fs.lastSavedStorageContents != "" {
		err = json.Unmarshal([]byte(fs.lastSavedStorageContents), &fs.storage)
		if err != nil {
			fs.logService.Error("Failed to parse state file", err)
			return err
		}
	}

	return nil
}

// GetItem retrieves an item from storage with a default value
func (fs *FileStorage) GetItem(key string, defaultValue interface{}) interface{} {
	fs.mutex.RLock()
	defer fs.mutex.RUnlock()

	if value, exists := fs.storage[key]; exists && value != nil {
		return value
	}
	return defaultValue
}

// SetItem stores an item in storage
func (fs *FileStorage) SetItem(key string, data interface{}) {
	fs.SetItems([]StateItem{{Key: key, Data: data}})
}

// SetItems stores multiple items in storage
func (fs *FileStorage) SetItems(items []StateItem) {
	fs.mutex.Lock()
	defer fs.mutex.Unlock()

	save := false

	for _, item := range items {
		// Shortcut for data that did not change
		if fs.storage[item.Key] == item.Data {
			continue
		}

		// Remove items when they are nil
		if item.Data == nil {
			if _, exists := fs.storage[item.Key]; exists {
				delete(fs.storage, item.Key)
				save = true
			}
		} else {
			// Otherwise add an item
			fs.storage[item.Key] = item.Data
			save = true
		}
	}

	if save {
		fs.save()
	}
}

// RemoveItem removes an item from storage
func (fs *FileStorage) RemoveItem(key string) {
	fs.mutex.Lock()
	defer fs.mutex.Unlock()

	if _, exists := fs.storage[key]; exists {
		delete(fs.storage, key)
		fs.save()
	}
}

// save triggers a save operation
func (fs *FileStorage) save() {
	fs.flushDelayer.Trigger(func() error {
		return fs.doSave()
	})
}

// doSave performs the actual save operation
func (fs *FileStorage) doSave() error {
	// Make sure initialization is complete
	if err := <-fs.initializing; err != nil {
		return err
	}

	fs.mutex.RLock()
	serializedDatabase, err := json.MarshalIndent(fs.storage, "", "    ")
	fs.mutex.RUnlock()

	if err != nil {
		fs.logService.Error("Failed to serialize state", err)
		return err
	}

	serializedStr := string(serializedDatabase)
	if serializedStr == fs.lastSavedStorageContents {
		return nil // No changes
	}

	// Write to disk
	buffer := basecommon.NewVSBuffer(serializedDatabase)
	_, err = fs.fileService.WriteFile(fs.storagePath, buffer, &filescommon.IFileWriteOptions{
		IFileAtomicWriteOptions: filescommon.IFileAtomicWriteOptions{
			Atomic: map[string]interface{}{"postfix": ".vsctmp"},
		},
	})
	if err != nil {
		fs.logService.Error("Failed to write state file", err)
		return err
	}

	fs.lastSavedStorageContents = serializedStr
	return nil
}

// Close closes the file storage and flushes any pending changes
func (fs *FileStorage) Close() error {
	fs.closeOnce.Do(func() {
		go func() {
			fs.closing <- fs.doSave()
		}()
	})

	return <-fs.closing
}

// StateReadonlyService provides read-only access to state
type StateReadonlyService struct {
	*basecommon.DisposableStore
	fileStorage *FileStorage
}

// NewStateReadonlyService creates a new read-only state service
func NewStateReadonlyService(
	saveStrategy SaveStrategy,
	environmentService environmentcommon.IEnvironmentService,
	logService logcommon.ILogService,
	fileService filescommon.IFileService,
) *StateReadonlyService {
	service := &StateReadonlyService{
		DisposableStore: basecommon.NewDisposableStore(),
	}

	service.fileStorage = NewFileStorage(
		environmentService.StateResource(),
		saveStrategy,
		logService,
		fileService,
	)
	service.Register(service.fileStorage)

	return service
}

// ServiceBrand implements the service brand
func (s *StateReadonlyService) ServiceBrand() interface{} {
	return "stateReadonlyService"
}

// Init initializes the state service
func (s *StateReadonlyService) Init() error {
	return s.fileStorage.Init()
}

// GetItem retrieves an item from state storage with a default value
func (s *StateReadonlyService) GetItem(key string, defaultValue interface{}) interface{} {
	return s.fileStorage.GetItem(key, defaultValue)
}

// GetItemOptional retrieves an item from state storage, returning nil if not found
func (s *StateReadonlyService) GetItemOptional(key string) interface{} {
	return s.fileStorage.GetItem(key, nil)
}

// StateService provides read-write access to state
type StateService struct {
	*StateReadonlyService
}

// NewStateService creates a new state service
func NewStateService(
	saveStrategy SaveStrategy,
	environmentService environmentcommon.IEnvironmentService,
	logService logcommon.ILogService,
	fileService filescommon.IFileService,
) *StateService {
	return &StateService{
		StateReadonlyService: NewStateReadonlyService(saveStrategy, environmentService, logService, fileService),
	}
}

// ServiceBrand implements the service brand
func (s *StateService) ServiceBrand() interface{} {
	return "stateService"
}

// SetItem stores an item in state storage
func (s *StateService) SetItem(key string, data interface{}) {
	s.fileStorage.SetItem(key, data)
}

// SetItems stores multiple items in state storage
func (s *StateService) SetItems(items []StateItem) {
	s.fileStorage.SetItems(items)
}

// RemoveItem removes an item from state storage
func (s *StateService) RemoveItem(key string) {
	s.fileStorage.RemoveItem(key)
}

// Close closes the state service and persists any pending changes
func (s *StateService) Close() error {
	return s.fileStorage.Close()
}
