/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
)

// FlowControlConstants defines constants for flow control
type FlowControlConstants struct {
	HighWatermarkChars int
	LowWatermarkChars  int
}

var FlowControlConstantsInstance = FlowControlConstants{
	HighWatermarkChars: 50000,
	LowWatermarkChars:  5000,
}

// ProcessPropertyType defines the types of process properties
type ProcessPropertyType int

const (
	ProcessPropertyTypeCwd ProcessPropertyType = iota
	ProcessPropertyTypeInitialCwd
	ProcessPropertyTypeFixedDimensions
	ProcessPropertyTypeTitle
	ProcessPropertyTypeShellType
	ProcessPropertyTypeHasChildProcesses
	ProcessPropertyTypeResolvedShellLaunchConfig
	ProcessPropertyTypeOverrideDimensions
	ProcessPropertyTypeFailedShellIntegrationActivation
	ProcessPropertyTypeUsedShellIntegrationInjection
	ProcessPropertyTypeShellIntegrationInjectionFailureReason
)

// TerminalShellType defines the types of terminal shells
type TerminalShellType int

const (
	TerminalShellTypeUnknown TerminalShellType = iota
	TerminalShellTypeBash
	TerminalShellTypeZsh
	TerminalShellTypeFish
	TerminalShellTypePowerShell
	TerminalShellTypeCmd
	TerminalShellTypeSh
	TerminalShellTypeCsh
	TerminalShellTypeKsh
)

// PosixShellType defines POSIX shell types
type PosixShellType int

const (
	PosixShellTypeBash PosixShellType = iota
	PosixShellTypeCsh
	PosixShellTypeFish
	PosixShellTypeKsh
	PosixShellTypeSh
	PosixShellTypeZsh
)

// GeneralShellType defines general shell types
type GeneralShellType int

const (
	GeneralShellTypePowerShell GeneralShellType = iota
	GeneralShellTypePython
	GeneralShellTypeJulia
	GeneralShellTypeNuShell
	GeneralShellTypeNode
)

// IProcessEnvironment represents the process environment
type IProcessEnvironment map[string]string

// IShellLaunchConfig represents the shell launch configuration
type IShellLaunchConfig struct {
	Executable  string
	Args        []string
	Cwd         interface{} // can be string or URI
	Env         IProcessEnvironment
	InitialText string
}

// ITerminalLaunchError represents a terminal launch error
type ITerminalLaunchError struct {
	Message string
}

// IProcessProperty represents a process property
type IProcessProperty struct {
	Type  ProcessPropertyType
	Value interface{}
}

// IProcessPropertyMap represents the process property map
type IProcessPropertyMap struct {
	Cwd                                    string
	InitialCwd                             string
	FixedDimensions                        *struct{ Cols, Rows *int }
	Title                                  string
	ShellType                              *TerminalShellType
	HasChildProcesses                      bool
	ResolvedShellLaunchConfig              interface{}
	OverrideDimensions                     *struct{ Cols, Rows *int }
	FailedShellIntegrationActivation       bool
	UsedShellIntegrationInjection          *bool
	ShellIntegrationInjectionFailureReason *string
}

// IProcessReadyWindowsPty represents Windows PTY information
type IProcessReadyWindowsPty struct {
	Backend     string
	BuildNumber int
}

// IProcessReadyEvent represents a process ready event
type IProcessReadyEvent struct {
	Pid        int
	Cwd        string
	WindowsPty *IProcessReadyWindowsPty
}

// ITerminalProcessOptions represents terminal process options
type ITerminalProcessOptions struct {
	WindowsEnableConpty bool
	WindowsUseConptyDll bool
}

// ITerminalChildProcess represents a terminal child process
type ITerminalChildProcess interface {
	GetId() int
	GetShouldPersist() bool
	GetExitMessage() *string
	GetCurrentTitle() string
	GetShellType() *TerminalShellType
	GetHasChildProcesses() bool

	// Events
	OnProcessData() basecommon.Event[string]
	OnProcessReady() basecommon.Event[*IProcessReadyEvent]
	OnDidChangeProperty() basecommon.Event[*IProcessProperty]
	OnProcessExit() basecommon.Event[int]

	// Methods
	Start() (interface{}, error) // returns ITerminalLaunchError or { injectedArgs: []string } or nil
	Input(data string, isBinary bool)
	SendSignal(signal string)
	ProcessBinary(data string) error
	RefreshProperty(propType ProcessPropertyType) (interface{}, error)
	UpdateProperty(propType ProcessPropertyType, value interface{}) error
	Resize(cols, rows int)
	ClearBuffer()
	AcknowledgeDataEvent(charCount int)
	ClearUnacknowledgedChars()
	SetUnicodeVersion(version string) error
	GetInitialCwd() (string, error)
	GetCwd() (string, error)
	GetWindowsPty() *IProcessReadyWindowsPty
	Shutdown(immediate bool)
}
