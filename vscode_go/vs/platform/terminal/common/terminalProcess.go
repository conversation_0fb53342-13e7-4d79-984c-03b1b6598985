/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"unicode/utf8"
)

// ChunkInput chunks input data to avoid overwhelming the terminal
func ChunkInput(data string) []string {
	if len(data) == 0 {
		return []string{}
	}

	// If data is small enough, return as is
	if len(data) <= 1024 {
		return []string{data}
	}

	var chunks []string
	remaining := data

	for len(remaining) > 0 {
		chunkSize := 1024
		if len(remaining) < chunkSize {
			chunkSize = len(remaining)
		}

		// Find a safe UTF-8 boundary
		chunk := remaining[:chunkSize]
		for chunkSize > 0 && !utf8.ValidString(chunk) {
			chunkSize--
			chunk = remaining[:chunkSize]
		}

		if chunkSize == 0 {
			// If we can't find a valid UTF-8 boundary, take at least one byte
			chunkSize = 1
			chunk = remaining[:chunkSize]
		}

		chunks = append(chunks, chunk)
		remaining = remaining[chunkSize:]
	}

	return chunks
}
