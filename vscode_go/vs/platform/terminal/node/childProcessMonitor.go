/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"os/exec"
	"strconv"
	"strings"
	"sync"
	"time"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	logcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
)

// ChildProcessMonitor monitors child processes of a terminal process
type ChildProcessMonitor struct {
	basecommon.Disposable

	pid               int
	logService        logcommon.ILogService
	hasChildProcesses bool
	lastActiveTime    time.Time
	mu                sync.RWMutex

	onDidChangeHasChildProcesses *basecommon.Emitter[bool]
}

// NewChildProcessMonitor creates a new child process monitor
func NewChildProcessMonitor(pid int, logService logcommon.ILogService) *ChildProcessMonitor {
	monitor := &ChildProcessMonitor{
		pid:                          pid,
		logService:                   logService,
		hasChildProcesses:            false,
		lastActiveTime:               time.Now(),
		onDidChangeHasChildProcesses: basecommon.NewEmitter[bool](),
	}

	monitor.Disposable = *basecommon.NewDisposable()
	monitor.Register(monitor.onDidChangeHasChildProcesses)

	return monitor
}

// OnDidChangeHasChildProcesses returns the event for child process changes
func (m *ChildProcessMonitor) OnDidChangeHasChildProcesses() basecommon.Event[bool] {
	return m.onDidChangeHasChildProcesses.Event()
}

// GetHasChildProcesses returns whether the process has child processes
func (m *ChildProcessMonitor) GetHasChildProcesses() bool {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.hasChildProcesses
}

// HandleOutput should be called when the process produces output
func (m *ChildProcessMonitor) HandleOutput() {
	m.mu.Lock()
	m.lastActiveTime = time.Now()
	m.mu.Unlock()

	m.checkForChildProcesses()
}

// HandleInput should be called when input is sent to the process
func (m *ChildProcessMonitor) HandleInput() {
	m.mu.Lock()
	m.lastActiveTime = time.Now()
	m.mu.Unlock()

	m.checkForChildProcesses()
}

// checkForChildProcesses checks if the process has child processes
func (m *ChildProcessMonitor) checkForChildProcesses() {
	hasChildren := m.checkChildProcesses()

	m.mu.Lock()
	previousState := m.hasChildProcesses
	m.hasChildProcesses = hasChildren
	m.mu.Unlock()

	if previousState != hasChildren {
		m.onDidChangeHasChildProcesses.Fire(hasChildren)
	}
}

// checkChildProcesses actually checks for child processes using platform-specific methods
func (m *ChildProcessMonitor) checkChildProcesses() bool {
	// Use ps command to check for child processes
	cmd := exec.Command("ps", "-o", "pid,ppid", "-ax")
	output, err := cmd.Output()
	if err != nil {
		m.logService.Trace("Failed to check child processes", err)
		return false
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		fields := strings.Fields(line)
		if len(fields) >= 2 {
			ppid, err := strconv.Atoi(fields[1])
			if err == nil && ppid == m.pid {
				// Found a child process
				return true
			}
		}
	}

	return false
}

// Dispose cleans up the monitor
func (m *ChildProcessMonitor) Dispose() {
	m.Disposable.Dispose()
}
