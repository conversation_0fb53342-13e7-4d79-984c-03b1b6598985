/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"os"
	"runtime"
	"strings"

	logcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
	productcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/product/common"
	terminalcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/terminal/common"
)

// IShellIntegrationConfigInjection represents shell integration configuration
type IShellIntegrationConfigInjection struct {
	NewArgs     []string
	EnvMixin    map[string]string
	FilesToCopy []struct {
		Source string
		Dest   string
	}
}

// ShellIntegrationInjection represents the result of shell integration injection
type ShellIntegrationInjection struct {
	Type   string // "injection" or "error"
	Reason string // error reason if Type is "error"
	IShellIntegrationConfigInjection
}

// GetShellIntegrationInjection gets shell integration injection configuration
func GetShellIntegrationInjection(
	shellLaunchConfig *terminalcommon.IShellLaunchConfig,
	options *terminalcommon.ITerminalProcessOptions,
	env map[string]string,
	logService logcommon.ILogService,
	productService productcommon.IProductService,
) (*ShellIntegrationInjection, error) {
	// For now, return a basic injection that doesn't modify anything
	// In a full implementation, this would analyze the shell and inject appropriate scripts
	return &ShellIntegrationInjection{
		Type: "injection",
		IShellIntegrationConfigInjection: IShellIntegrationConfigInjection{
			NewArgs:     nil,
			EnvMixin:    make(map[string]string),
			FilesToCopy: nil,
		},
	}, nil
}

// GetWindowsBuildNumber gets the Windows build number
func GetWindowsBuildNumber() int {
	if runtime.GOOS != "windows" {
		return 0
	}

	// On Windows, this would typically read from the registry
	// For now, return a reasonable default
	return 19045 // Windows 10 21H2
}

// GetWindowsReleaseId gets the Windows release ID
func GetWindowsReleaseId() string {
	if runtime.GOOS != "windows" {
		return ""
	}

	// On Windows, this would typically read from the registry
	// For now, return a reasonable default
	return "21H2"
}

// IsWindows11 checks if running on Windows 11
func IsWindows11() bool {
	if runtime.GOOS != "windows" {
		return false
	}

	buildNumber := GetWindowsBuildNumber()
	return buildNumber >= 22000
}

// GetShellEnvironmentForTerminal gets the shell environment for a terminal
func GetShellEnvironmentForTerminal(
	shellLaunchConfig *terminalcommon.IShellLaunchConfig,
	logService logcommon.ILogService,
) (map[string]string, error) {
	env := make(map[string]string)

	// Copy current environment
	for _, envVar := range os.Environ() {
		parts := strings.SplitN(envVar, "=", 2)
		if len(parts) == 2 {
			env[parts[0]] = parts[1]
		}
	}

	// Apply shell launch config environment
	if shellLaunchConfig.Env != nil {
		for key, value := range shellLaunchConfig.Env {
			env[key] = value
		}
	}

	return env, nil
}

// CreateTerminalEnvironment creates a terminal environment
func CreateTerminalEnvironment(
	shellLaunchConfig *terminalcommon.IShellLaunchConfig,
	env map[string]string,
	logService logcommon.ILogService,
) map[string]string {
	result := make(map[string]string)

	// Copy base environment
	for key, value := range env {
		result[key] = value
	}

	// Add terminal-specific environment variables
	result["TERM"] = "xterm-256color"
	result["COLORTERM"] = "truecolor"

	// Platform-specific adjustments
	switch runtime.GOOS {
	case "darwin":
		if result["TERM_PROGRAM"] == "" {
			result["TERM_PROGRAM"] = "vscode"
		}
	case "linux":
		if result["TERM"] == "" {
			result["TERM"] = "xterm-256color"
		}
	case "windows":
		// Windows-specific terminal environment
		if result["TERM"] == "" {
			result["TERM"] = "xterm"
		}
	}

	return result
}
