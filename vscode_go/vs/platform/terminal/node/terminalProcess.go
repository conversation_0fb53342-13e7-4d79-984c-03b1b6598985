/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"bufio"
	"errors"
	"fmt"
	"io"
	"os"
	"os/exec"
	"path/filepath"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"time"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	basenode "github.com/yudaprama/kawai-agent/vscode_go/vs/base/node"
	logcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
	productcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/product/common"
	terminalcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/terminal/common"
)

// ShutdownConstants defines constants for shutdown behavior
const (
	DataFlushTimeoutMs    = 250
	MaximumShutdownTimeMs = 5000
)

// Constants for kill/spawn throttling
const (
	KillSpawnThrottleIntervalMs = 250
	KillSpawnSpacingDurationMs  = 50
	WriteIntervalMs             = 5
)

// IWriteObject represents a write operation
type IWriteObject struct {
	Data     string
	IsBinary bool
}

// TerminalProcess implements ITerminalChildProcess for managing terminal processes
type TerminalProcess struct {
	basecommon.Disposable

	id                     int
	shouldPersist          bool
	properties             *terminalcommon.IProcessPropertyMap
	exitCode               *int
	exitMessage            *string
	closeTimeout           *time.Timer
	ptyProcess             *exec.Cmd
	ptyStdin               io.WriteCloser
	ptyStdout              io.ReadCloser
	ptyStderr              io.ReadCloser
	currentTitle           string
	processStartupComplete *sync.WaitGroup
	windowsShellHelper     *WindowsShellHelper
	childProcessMonitor    *ChildProcessMonitor
	titleInterval          *time.Ticker
	writeQueue             []IWriteObject
	writeTimeout           *time.Timer
	delayedResizer         *DelayedResizer
	initialCwd             string

	isPtyPaused             bool
	unacknowledgedCharCount int
	mu                      sync.RWMutex

	// Static field for throttling
	lastKillOrStart time.Time

	// Event emitters
	onProcessData       *basecommon.Emitter[string]
	onProcessReady      *basecommon.Emitter[*terminalcommon.IProcessReadyEvent]
	onDidChangeProperty *basecommon.Emitter[*terminalcommon.IProcessProperty]
	onProcessExit       *basecommon.Emitter[int]

	// Dependencies
	shellLaunchConfig *terminalcommon.IShellLaunchConfig
	executableEnv     terminalcommon.IProcessEnvironment
	options           *terminalcommon.ITerminalProcessOptions
	logService        logcommon.ILogService
	productService    productcommon.IProductService
}

// DelayedResizer tracks the latest resize event to be triggered at a later point
type DelayedResizer struct {
	basecommon.Disposable

	Rows    *int
	Cols    *int
	timeout *time.Timer

	onTrigger *basecommon.Emitter[*struct{ Rows, Cols *int }]
}

// NewDelayedResizer creates a new delayed resizer
func NewDelayedResizer() *DelayedResizer {
	resizer := &DelayedResizer{
		onTrigger: basecommon.NewEmitter[*struct{ Rows, Cols *int }](),
	}

	resizer.Disposable = *basecommon.NewDisposable()
	resizer.Register(resizer.onTrigger)

	resizer.timeout = time.AfterFunc(time.Second, func() {
		resizer.onTrigger.Fire(&struct{ Rows, Cols *int }{
			Rows: resizer.Rows,
			Cols: resizer.Cols,
		})
	})

	return resizer
}

// OnTrigger returns the trigger event
func (d *DelayedResizer) OnTrigger() basecommon.Event[*struct{ Rows, Cols *int }] {
	return d.onTrigger.Event()
}

// Dispose cleans up the resizer
func (d *DelayedResizer) Dispose() {
	if d.timeout != nil {
		d.timeout.Stop()
	}
	d.Disposable.Dispose()
}

// NewTerminalProcess creates a new terminal process
func NewTerminalProcess(
	shellLaunchConfig *terminalcommon.IShellLaunchConfig,
	cwd string,
	cols, rows int,
	env terminalcommon.IProcessEnvironment,
	executableEnv terminalcommon.IProcessEnvironment,
	options *terminalcommon.ITerminalProcessOptions,
	logService logcommon.ILogService,
	productService productcommon.IProductService,
) *TerminalProcess {
	process := &TerminalProcess{
		id:                     0,
		shouldPersist:          false,
		shellLaunchConfig:      shellLaunchConfig,
		executableEnv:          executableEnv,
		options:                options,
		logService:             logService,
		productService:         productService,
		initialCwd:             cwd,
		processStartupComplete: &sync.WaitGroup{},
		writeQueue:             make([]IWriteObject, 0),

		// Initialize event emitters
		onProcessData:       basecommon.NewEmitter[string](),
		onProcessReady:      basecommon.NewEmitter[*terminalcommon.IProcessReadyEvent](),
		onDidChangeProperty: basecommon.NewEmitter[*terminalcommon.IProcessProperty](),
		onProcessExit:       basecommon.NewEmitter[int](),
	}

	// Initialize properties
	process.properties = &terminalcommon.IProcessPropertyMap{
		Cwd:                                    cwd,
		InitialCwd:                             cwd,
		FixedDimensions:                        &struct{ Cols, Rows *int }{Cols: &cols, Rows: &rows},
		Title:                                  "",
		ShellType:                              nil,
		HasChildProcesses:                      true,
		ResolvedShellLaunchConfig:              struct{}{},
		OverrideDimensions:                     nil,
		FailedShellIntegrationActivation:       false,
		UsedShellIntegrationInjection:          nil,
		ShellIntegrationInjectionFailureReason: nil,
	}

	process.Disposable = *basecommon.NewDisposable()
	process.Register(process.onProcessData)
	process.Register(process.onProcessReady)
	process.Register(process.onDidChangeProperty)
	process.Register(process.onProcessExit)

	// Handle delayed resizing for Windows Git Bash
	if runtime.GOOS == "windows" && cols == 0 && rows == 0 &&
		shellLaunchConfig.Executable != "" &&
		strings.HasSuffix(shellLaunchConfig.Executable, "Git\\bin\\bash.exe") {

		process.delayedResizer = NewDelayedResizer()
		process.Register(process.delayedResizer)

		disposable := process.delayedResizer.OnTrigger().Subscribe(func(dimensions *struct{ Rows, Cols *int }) {
			if process.delayedResizer != nil {
				process.delayedResizer.Dispose()
				process.delayedResizer = nil
			}
			if dimensions.Cols != nil && dimensions.Rows != nil {
				process.Resize(*dimensions.Cols, *dimensions.Rows)
			}
		})
		process.Register(disposable)
	}

	return process
}

// Interface implementation methods

// GetId returns the process ID
func (p *TerminalProcess) GetId() int {
	return p.id
}

// GetShouldPersist returns whether the process should persist
func (p *TerminalProcess) GetShouldPersist() bool {
	return p.shouldPersist
}

// GetExitMessage returns the exit message
func (p *TerminalProcess) GetExitMessage() *string {
	return p.exitMessage
}

// GetCurrentTitle returns the current title
func (p *TerminalProcess) GetCurrentTitle() string {
	if p.windowsShellHelper != nil {
		return p.windowsShellHelper.GetShellTitle()
	}
	return p.currentTitle
}

// GetShellType returns the shell type
func (p *TerminalProcess) GetShellType() *terminalcommon.TerminalShellType {
	if runtime.GOOS == "windows" && p.windowsShellHelper != nil {
		return p.windowsShellHelper.GetShellType()
	}

	// For POSIX systems, detect from current title
	title := strings.ToLower(p.currentTitle)
	switch {
	case strings.Contains(title, "bash"):
		shellType := terminalcommon.TerminalShellTypeBash
		return &shellType
	case strings.Contains(title, "zsh"):
		shellType := terminalcommon.TerminalShellTypeZsh
		return &shellType
	case strings.Contains(title, "fish"):
		shellType := terminalcommon.TerminalShellTypeFish
		return &shellType
	case strings.Contains(title, "python"):
		shellType := terminalcommon.TerminalShellTypeUnknown // Would need GeneralShellType
		return &shellType
	default:
		return nil
	}
}

// GetHasChildProcesses returns whether the process has child processes
func (p *TerminalProcess) GetHasChildProcesses() bool {
	if p.childProcessMonitor != nil {
		return p.childProcessMonitor.GetHasChildProcesses()
	}
	return false
}

// Event getters

// OnProcessData returns the process data event
func (p *TerminalProcess) OnProcessData() basecommon.Event[string] {
	return p.onProcessData.Event()
}

// OnProcessReady returns the process ready event
func (p *TerminalProcess) OnProcessReady() basecommon.Event[*terminalcommon.IProcessReadyEvent] {
	return p.onProcessReady.Event()
}

// OnDidChangeProperty returns the property change event
func (p *TerminalProcess) OnDidChangeProperty() basecommon.Event[*terminalcommon.IProcessProperty] {
	return p.onDidChangeProperty.Event()
}

// OnProcessExit returns the process exit event
func (p *TerminalProcess) OnProcessExit() basecommon.Event[int] {
	return p.onProcessExit.Event()
}

// Start starts the terminal process
func (p *TerminalProcess) Start() (interface{}, error) {
	// Validate CWD
	if err := p.validateCwd(); err != nil {
		return &terminalcommon.ITerminalLaunchError{Message: err.Error()}, nil
	}

	// Validate executable
	if err := p.validateExecutable(); err != nil {
		return &terminalcommon.ITerminalLaunchError{Message: err.Error()}, nil
	}

	// Get shell integration injection
	injection, err := GetShellIntegrationInjection(
		p.shellLaunchConfig,
		p.options,
		p.executableEnv,
		p.logService,
		p.productService,
	)
	if err != nil {
		return &terminalcommon.ITerminalLaunchError{Message: err.Error()}, nil
	}

	if injection.Type == "injection" {
		p.onDidChangeProperty.Fire(&terminalcommon.IProcessProperty{
			Type:  terminalcommon.ProcessPropertyTypeUsedShellIntegrationInjection,
			Value: true,
		})
	} else {
		p.onDidChangeProperty.Fire(&terminalcommon.IProcessProperty{
			Type:  terminalcommon.ProcessPropertyTypeFailedShellIntegrationActivation,
			Value: true,
		})
		p.onDidChangeProperty.Fire(&terminalcommon.IProcessProperty{
			Type:  terminalcommon.ProcessPropertyTypeShellIntegrationInjectionFailureReason,
			Value: injection.Reason,
		})
	}

	// Setup PTY process
	if err := p.setupPtyProcess(injection); err != nil {
		p.logService.Trace("Failed to spawn PTY process", err)
		return &terminalcommon.ITerminalLaunchError{Message: fmt.Sprintf("A native exception occurred during launch (%v)", err)}, nil
	}

	if injection.Type == "injection" && injection.NewArgs != nil {
		return map[string]interface{}{"injectedArgs": injection.NewArgs}, nil
	}

	return nil, nil
}

// validateCwd validates the current working directory
func (p *TerminalProcess) validateCwd() error {
	stat, err := os.Stat(p.initialCwd)
	if err != nil {
		if os.IsNotExist(err) {
			return fmt.Errorf("Starting directory (cwd) \"%s\" does not exist", p.initialCwd)
		}
		return err
	}

	if !stat.IsDir() {
		return fmt.Errorf("Starting directory (cwd) \"%s\" is not a directory", p.initialCwd)
	}

	p.onDidChangeProperty.Fire(&terminalcommon.IProcessProperty{
		Type:  terminalcommon.ProcessPropertyTypeInitialCwd,
		Value: p.initialCwd,
	})

	return nil
}

// validateExecutable validates the executable
func (p *TerminalProcess) validateExecutable() error {
	if p.shellLaunchConfig.Executable == "" {
		return errors.New("IShellLaunchConfig.Executable not set")
	}

	var cwd string
	if p.shellLaunchConfig.Cwd != nil {
		if cwdStr, ok := p.shellLaunchConfig.Cwd.(string); ok {
			cwd = cwdStr
		}
	}

	var envPaths []string
	if p.shellLaunchConfig.Env != nil {
		if pathVar, exists := p.shellLaunchConfig.Env["PATH"]; exists {
			envPaths = strings.Split(pathVar, string(os.PathListSeparator))
		}
	}

	// Convert environment to map[string]string
	envMap := make(map[string]string)
	for key, value := range p.executableEnv {
		envMap[key] = value
	}

	executable, err := basenode.FindExecutable(p.shellLaunchConfig.Executable, cwd, envPaths, envMap, nil)
	if err != nil {
		return fmt.Errorf("Path to shell executable \"%s\" does not exist", p.shellLaunchConfig.Executable)
	}

	stat, err := os.Stat(executable)
	if err != nil {
		return err
	}

	if !stat.Mode().IsRegular() {
		return fmt.Errorf("Path to shell executable \"%s\" is not a file or a symlink", p.shellLaunchConfig.Executable)
	}

	// Set the executable explicitly
	p.shellLaunchConfig.Executable = executable

	return nil
}

// setupPtyProcess sets up the PTY process
func (p *TerminalProcess) setupPtyProcess(injection *ShellIntegrationInjection) error {
	args := p.shellLaunchConfig.Args
	if injection.Type == "injection" && injection.NewArgs != nil {
		args = injection.NewArgs
	}

	if err := p.throttleKillSpawn(); err != nil {
		return err
	}

	p.logService.Trace("Spawning PTY process", p.shellLaunchConfig.Executable, args)

	// Create the command
	cmd := exec.Command(p.shellLaunchConfig.Executable, args...)
	cmd.Dir = p.initialCwd

	// Set up environment
	env := CreateTerminalEnvironment(p.shellLaunchConfig, p.executableEnv, p.logService)
	if injection.Type == "injection" && injection.EnvMixin != nil {
		for key, value := range injection.EnvMixin {
			env[key] = value
		}
	}

	// Convert environment to string slice
	var envSlice []string
	for key, value := range env {
		envSlice = append(envSlice, fmt.Sprintf("%s=%s", key, value))
	}
	cmd.Env = envSlice

	// Set up pipes
	stdin, err := cmd.StdinPipe()
	if err != nil {
		return err
	}

	stdout, err := cmd.StdoutPipe()
	if err != nil {
		return err
	}

	stderr, err := cmd.StderrPipe()
	if err != nil {
		return err
	}

	// Start the process
	if err := cmd.Start(); err != nil {
		return err
	}

	p.ptyProcess = cmd
	p.ptyStdin = stdin
	p.ptyStdout = stdout
	p.ptyStderr = stderr

	// Setup child process monitoring
	p.childProcessMonitor = NewChildProcessMonitor(cmd.Process.Pid, p.logService)
	p.Register(p.childProcessMonitor)

	disposable := p.childProcessMonitor.OnDidChangeHasChildProcesses().Subscribe(func(value bool) {
		p.onDidChangeProperty.Fire(&terminalcommon.IProcessProperty{
			Type:  terminalcommon.ProcessPropertyTypeHasChildProcesses,
			Value: value,
		})
	})
	p.Register(disposable)

	p.processStartupComplete.Add(1)

	// Handle process ready
	go func() {
		defer p.processStartupComplete.Done()
		p.sendProcessId(cmd.Process.Pid)
	}()

	// Handle stdout
	go p.handleStdout()

	// Handle stderr
	go p.handleStderr()

	// Handle process exit
	go p.handleProcessExit()

	// Setup title polling
	p.setupTitlePolling()

	// Setup Windows shell helper
	if runtime.GOOS == "windows" {
		go func() {
			p.processStartupComplete.Wait()
			p.windowsShellHelper = NewWindowsShellHelper(cmd.Process.Pid)
			p.Register(p.windowsShellHelper)

			disposable1 := p.windowsShellHelper.OnShellTypeChanged().Subscribe(func(shellType terminalcommon.TerminalShellType) {
				p.onDidChangeProperty.Fire(&terminalcommon.IProcessProperty{
					Type:  terminalcommon.ProcessPropertyTypeShellType,
					Value: shellType,
				})
			})
			p.Register(disposable1)

			disposable2 := p.windowsShellHelper.OnShellNameChanged().Subscribe(func(name string) {
				p.onDidChangeProperty.Fire(&terminalcommon.IProcessProperty{
					Type:  terminalcommon.ProcessPropertyTypeTitle,
					Value: name,
				})
			})
			p.Register(disposable2)
		}()
	}

	return nil
}

// handleStdout handles stdout from the PTY process
func (p *TerminalProcess) handleStdout() {
	scanner := bufio.NewScanner(p.ptyStdout)
	for scanner.Scan() {
		data := scanner.Text() + "\n"

		// Handle flow control
		p.mu.Lock()
		p.unacknowledgedCharCount += len(data)
		if !p.isPtyPaused && p.unacknowledgedCharCount > terminalcommon.FlowControlConstantsInstance.HighWatermarkChars {
			p.logService.Trace(fmt.Sprintf("Flow control: Pause (%d > %d)", p.unacknowledgedCharCount, terminalcommon.FlowControlConstantsInstance.HighWatermarkChars))
			p.isPtyPaused = true
			// In a real implementation, we would pause the PTY here
		}
		p.mu.Unlock()

		// Fire data event
		p.logService.Trace("PTY data", data)
		p.onProcessData.Fire(data)

		// Handle close timeout
		if p.closeTimeout != nil {
			p.queueProcessExit()
		}

		// Update monitoring
		if p.windowsShellHelper != nil {
			p.windowsShellHelper.CheckShell()
		}
		if p.childProcessMonitor != nil {
			p.childProcessMonitor.HandleOutput()
		}
	}
}

// handleStderr handles stderr from the PTY process
func (p *TerminalProcess) handleStderr() {
	scanner := bufio.NewScanner(p.ptyStderr)
	for scanner.Scan() {
		data := scanner.Text() + "\n"
		p.logService.Trace("PTY stderr", data)
		p.onProcessData.Fire(data)
	}
}

// handleProcessExit handles process exit
func (p *TerminalProcess) handleProcessExit() {
	if p.ptyProcess != nil {
		err := p.ptyProcess.Wait()
		if err != nil {
			if exitError, ok := err.(*exec.ExitError); ok {
				exitCode := exitError.ProcessState.ExitCode()
				p.exitCode = &exitCode
			}
		} else {
			exitCode := 0
			p.exitCode = &exitCode
		}

		p.queueProcessExit()
	}
}

// sendProcessId sends the process ID
func (p *TerminalProcess) sendProcessId(pid int) {
	event := &terminalcommon.IProcessReadyEvent{
		Pid: pid,
		Cwd: p.initialCwd,
	}

	if runtime.GOOS == "windows" {
		event.WindowsPty = &terminalcommon.IProcessReadyWindowsPty{
			Backend:     "conpty", // Simplified for now
			BuildNumber: GetWindowsBuildNumber(),
		}
	}

	p.onProcessReady.Fire(event)
}

// setupTitlePolling sets up title polling
func (p *TerminalProcess) setupTitlePolling() {
	// Send initial title
	go func() {
		time.Sleep(100 * time.Millisecond)
		p.sendProcessTitle()
	}()

	// Setup polling for non-Windows
	if runtime.GOOS != "windows" {
		p.titleInterval = time.NewTicker(200 * time.Millisecond)
		go func() {
			for range p.titleInterval.C {
				p.sendProcessTitle()
			}
		}()
	}
}

// sendProcessTitle sends the process title
func (p *TerminalProcess) sendProcessTitle() {
	if p.ptyProcess == nil {
		return
	}

	// Get process name (simplified implementation)
	title := filepath.Base(p.shellLaunchConfig.Executable)

	p.mu.Lock()
	oldTitle := p.currentTitle
	p.currentTitle = title
	p.mu.Unlock()

	if oldTitle != title {
		p.onDidChangeProperty.Fire(&terminalcommon.IProcessProperty{
			Type:  terminalcommon.ProcessPropertyTypeTitle,
			Value: title,
		})

		// Detect shell type from title
		p.detectShellTypeFromTitle(title)
	}
}

// detectShellTypeFromTitle detects shell type from title
func (p *TerminalProcess) detectShellTypeFromTitle(title string) {
	sanitizedTitle := strings.ToLower(filepath.Base(title))

	var shellType *terminalcommon.TerminalShellType

	switch {
	case strings.Contains(sanitizedTitle, "python"):
		st := terminalcommon.TerminalShellTypeUnknown // Would be GeneralShellType.Python
		shellType = &st
	case strings.Contains(sanitizedTitle, "julia"):
		st := terminalcommon.TerminalShellTypeUnknown // Would be GeneralShellType.Julia
		shellType = &st
	case strings.Contains(sanitizedTitle, "bash"):
		st := terminalcommon.TerminalShellTypeBash
		shellType = &st
	case strings.Contains(sanitizedTitle, "zsh"):
		st := terminalcommon.TerminalShellTypeZsh
		shellType = &st
	case strings.Contains(sanitizedTitle, "fish"):
		st := terminalcommon.TerminalShellTypeFish
		shellType = &st
	}

	if shellType != nil {
		p.onDidChangeProperty.Fire(&terminalcommon.IProcessProperty{
			Type:  terminalcommon.ProcessPropertyTypeShellType,
			Value: *shellType,
		})
	}
}

// queueProcessExit queues the process exit
func (p *TerminalProcess) queueProcessExit() {
	if p.logService.GetLevel() == logcommon.LogLevelTrace {
		p.logService.Trace("TerminalProcess#queueProcessExit")
	}

	if p.closeTimeout != nil {
		p.closeTimeout.Stop()
	}

	p.closeTimeout = time.AfterFunc(DataFlushTimeoutMs*time.Millisecond, func() {
		p.closeTimeout = nil
		p.kill()
	})
}

// kill kills the process
func (p *TerminalProcess) kill() {
	// Wait for startup to complete
	p.processStartupComplete.Wait()

	if p.IsDisposed() {
		return
	}

	// Kill the process
	if p.ptyProcess != nil {
		if err := p.throttleKillSpawn(); err == nil {
			p.logService.Trace("Killing PTY process")
			p.ptyProcess.Process.Kill()
		}
	}

	exitCode := 0
	if p.exitCode != nil {
		exitCode = *p.exitCode
	}

	p.onProcessExit.Fire(exitCode)
	p.Dispose()
}

// throttleKillSpawn throttles kill/spawn operations
func (p *TerminalProcess) throttleKillSpawn() error {
	// Only throttle on Windows
	if runtime.GOOS != "windows" {
		return nil
	}

	// Use a loop to ensure multiple calls in a single interval space out
	for time.Since(p.lastKillOrStart) < KillSpawnThrottleIntervalMs*time.Millisecond {
		p.logService.Trace("Throttling kill/spawn call")
		time.Sleep(KillSpawnThrottleIntervalMs*time.Millisecond - time.Since(p.lastKillOrStart) + KillSpawnSpacingDurationMs*time.Millisecond)
	}

	p.lastKillOrStart = time.Now()
	return nil
}

// Shutdown shuts down the process
func (p *TerminalProcess) Shutdown(immediate bool) {
	if p.logService.GetLevel() == logcommon.LogLevelTrace {
		p.logService.Trace("TerminalProcess#Shutdown")
	}

	// Don't force immediate disposal on Windows as mitigation
	if immediate && runtime.GOOS != "windows" {
		p.kill()
	} else {
		if p.closeTimeout == nil && !p.IsDisposed() {
			p.queueProcessExit()

			// Allow maximum time for process to exit
			time.AfterFunc(MaximumShutdownTimeMs*time.Millisecond, func() {
				if p.closeTimeout != nil && !p.IsDisposed() {
					p.closeTimeout = nil
					p.kill()
				}
			})
		}
	}
}

// Input sends input to the process
func (p *TerminalProcess) Input(data string, isBinary bool) {
	if p.IsDisposed() || p.ptyProcess == nil {
		return
	}

	chunks := terminalcommon.ChunkInput(data)
	for _, chunk := range chunks {
		p.writeQueue = append(p.writeQueue, IWriteObject{
			Data:     chunk,
			IsBinary: isBinary,
		})
	}

	p.startWrite()
}

// startWrite starts writing to the process
func (p *TerminalProcess) startWrite() {
	if p.writeTimeout != nil || len(p.writeQueue) == 0 {
		return
	}

	p.doWrite()

	if len(p.writeQueue) == 0 {
		p.writeTimeout = nil
		return
	}

	p.writeTimeout = time.AfterFunc(WriteIntervalMs*time.Millisecond, func() {
		p.writeTimeout = nil
		p.startWrite()
	})
}

// doWrite performs the actual write
func (p *TerminalProcess) doWrite() {
	if len(p.writeQueue) == 0 {
		return
	}

	obj := p.writeQueue[0]
	p.writeQueue = p.writeQueue[1:]

	p.logService.Trace("PTY write", obj.Data)

	if obj.IsBinary {
		p.ptyStdin.Write([]byte(obj.Data))
	} else {
		p.ptyStdin.Write([]byte(obj.Data))
	}

	if p.childProcessMonitor != nil {
		p.childProcessMonitor.HandleInput()
	}
}

// SendSignal sends a signal to the process
func (p *TerminalProcess) SendSignal(signal string) {
	if p.IsDisposed() || p.ptyProcess == nil {
		return
	}

	// Signal sending would be implemented here
	// For now, we'll just log it
	p.logService.Trace("Sending signal", signal)
}

// ProcessBinary processes binary data
func (p *TerminalProcess) ProcessBinary(data string) error {
	p.Input(data, true)
	return nil
}

// RefreshProperty refreshes a property
func (p *TerminalProcess) RefreshProperty(propType terminalcommon.ProcessPropertyType) (interface{}, error) {
	switch propType {
	case terminalcommon.ProcessPropertyTypeCwd:
		cwd, err := p.GetCwd()
		if err != nil {
			return nil, err
		}

		p.mu.Lock()
		if p.properties.Cwd != cwd {
			p.properties.Cwd = cwd
			p.mu.Unlock()
			p.onDidChangeProperty.Fire(&terminalcommon.IProcessProperty{
				Type:  terminalcommon.ProcessPropertyTypeCwd,
				Value: cwd,
			})
		} else {
			p.mu.Unlock()
		}
		return cwd, nil

	case terminalcommon.ProcessPropertyTypeInitialCwd:
		initialCwd, err := p.GetInitialCwd()
		if err != nil {
			return nil, err
		}

		p.mu.Lock()
		if p.properties.InitialCwd != initialCwd {
			p.properties.InitialCwd = initialCwd
			p.mu.Unlock()
			p.onDidChangeProperty.Fire(&terminalcommon.IProcessProperty{
				Type:  terminalcommon.ProcessPropertyTypeInitialCwd,
				Value: initialCwd,
			})
		} else {
			p.mu.Unlock()
		}
		return initialCwd, nil

	case terminalcommon.ProcessPropertyTypeTitle:
		return p.GetCurrentTitle(), nil

	default:
		return p.GetShellType(), nil
	}
}

// UpdateProperty updates a property
func (p *TerminalProcess) UpdateProperty(propType terminalcommon.ProcessPropertyType, value interface{}) error {
	if propType == terminalcommon.ProcessPropertyTypeFixedDimensions {
		if dims, ok := value.(*struct{ Cols, Rows *int }); ok {
			p.mu.Lock()
			p.properties.FixedDimensions = dims
			p.mu.Unlock()
		}
	}
	return nil
}

// Resize resizes the terminal
func (p *TerminalProcess) Resize(cols, rows int) {
	if p.IsDisposed() {
		return
	}

	if cols <= 0 || rows <= 0 {
		return
	}

	if p.ptyProcess == nil {
		return
	}

	// Ensure minimum dimensions
	if cols < 1 {
		cols = 1
	}
	if rows < 1 {
		rows = 1
	}

	// Handle delayed resize
	if p.delayedResizer != nil {
		p.delayedResizer.Cols = &cols
		p.delayedResizer.Rows = &rows
		return
	}

	p.logService.Trace("PTY resize", cols, rows)

	// Resize implementation would go here
	// For now, we'll just log it
}

// ClearBuffer clears the buffer
func (p *TerminalProcess) ClearBuffer() {
	// Clear buffer implementation would go here
}

// AcknowledgeDataEvent acknowledges data event
func (p *TerminalProcess) AcknowledgeDataEvent(charCount int) {
	p.mu.Lock()
	p.unacknowledgedCharCount = max(p.unacknowledgedCharCount-charCount, 0)
	p.logService.Trace(fmt.Sprintf("Flow control: Ack %d chars (unacknowledged: %d)", charCount, p.unacknowledgedCharCount))

	if p.isPtyPaused && p.unacknowledgedCharCount < terminalcommon.FlowControlConstantsInstance.LowWatermarkChars {
		p.logService.Trace(fmt.Sprintf("Flow control: Resume (%d < %d)", p.unacknowledgedCharCount, terminalcommon.FlowControlConstantsInstance.LowWatermarkChars))
		// In a real implementation, we would resume the PTY here
		p.isPtyPaused = false
	}
	p.mu.Unlock()
}

// ClearUnacknowledgedChars clears unacknowledged characters
func (p *TerminalProcess) ClearUnacknowledgedChars() {
	p.mu.Lock()
	p.unacknowledgedCharCount = 0
	p.logService.Trace("Flow control: Cleared all unacknowledged chars, forcing resume")
	if p.isPtyPaused {
		// In a real implementation, we would resume the PTY here
		p.isPtyPaused = false
	}
	p.mu.Unlock()
}

// SetUnicodeVersion sets the unicode version
func (p *TerminalProcess) SetUnicodeVersion(version string) error {
	// No-op for now
	return nil
}

// GetInitialCwd returns the initial CWD
func (p *TerminalProcess) GetInitialCwd() (string, error) {
	return p.initialCwd, nil
}

// GetCwd returns the current working directory
func (p *TerminalProcess) GetCwd() (string, error) {
	if p.ptyProcess == nil {
		return p.initialCwd, nil
	}

	switch runtime.GOOS {
	case "darwin":
		// Use lsof on macOS
		cmd := exec.Command("lsof", "-OPln", "-p", strconv.Itoa(p.ptyProcess.Process.Pid))
		cmd.Env = append(cmd.Env, "LANG=en_US.UTF-8")

		output, err := cmd.Output()
		if err != nil {
			p.logService.Error("lsof did not run successfully", err)
			return p.initialCwd, nil
		}

		lines := strings.Split(string(output), "\n")
		for _, line := range lines {
			if strings.Contains(line, " cwd ") {
				parts := strings.Fields(line)
				if len(parts) > 0 {
					return parts[len(parts)-1], nil
				}
			}
		}

		return p.initialCwd, nil

	case "linux":
		// Use /proc on Linux
		linkPath := fmt.Sprintf("/proc/%d/cwd", p.ptyProcess.Process.Pid)
		cwd, err := os.Readlink(linkPath)
		if err != nil {
			return p.initialCwd, nil
		}
		return cwd, nil

	default:
		return p.initialCwd, nil
	}
}

// GetWindowsPty returns Windows PTY information
func (p *TerminalProcess) GetWindowsPty() *terminalcommon.IProcessReadyWindowsPty {
	if runtime.GOOS == "windows" {
		return &terminalcommon.IProcessReadyWindowsPty{
			Backend:     "conpty",
			BuildNumber: GetWindowsBuildNumber(),
		}
	}
	return nil
}

// Dispose cleans up the process
func (p *TerminalProcess) Dispose() {
	if p.titleInterval != nil {
		p.titleInterval.Stop()
	}

	if p.closeTimeout != nil {
		p.closeTimeout.Stop()
	}

	if p.writeTimeout != nil {
		p.writeTimeout.Stop()
	}

	if p.ptyStdin != nil {
		p.ptyStdin.Close()
	}

	if p.ptyStdout != nil {
		p.ptyStdout.Close()
	}

	if p.ptyStderr != nil {
		p.ptyStderr.Close()
	}

	p.Disposable.Dispose()
}

// Helper function for max
func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}
