/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"os/exec"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"time"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	terminalcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/terminal/common"
)

// WindowsShellHelper provides Windows-specific shell functionality
type WindowsShellHelper struct {
	basecommon.Disposable

	pid             int
	shellType       *terminalcommon.TerminalShellType
	shellTitle      string
	mu              sync.RWMutex
	polling         bool
	pollingInterval time.Duration

	onShellTypeChanged *basecommon.Emitter[terminalcommon.TerminalShellType]
	onShellNameChanged *basecommon.Emitter[string]
}

// NewWindowsShellHelper creates a new Windows shell helper
func NewWindowsShellHelper(pid int) *WindowsShellHelper {
	helper := &WindowsShellHelper{
		pid:                pid,
		polling:            false,
		pollingInterval:    time.Second * 2,
		onShellTypeChanged: basecommon.NewEmitter[terminalcommon.TerminalShellType](),
		onShellNameChanged: basecommon.NewEmitter[string](),
	}

	helper.Disposable = *basecommon.NewDisposable()
	helper.Register(helper.onShellTypeChanged)
	helper.Register(helper.onShellNameChanged)

	// Only enable on Windows
	if runtime.GOOS == "windows" {
		helper.startPolling()
	}

	return helper
}

// OnShellTypeChanged returns the event for shell type changes
func (w *WindowsShellHelper) OnShellTypeChanged() basecommon.Event[terminalcommon.TerminalShellType] {
	return w.onShellTypeChanged.Event()
}

// OnShellNameChanged returns the event for shell name changes
func (w *WindowsShellHelper) OnShellNameChanged() basecommon.Event[string] {
	return w.onShellNameChanged.Event()
}

// GetShellType returns the current shell type
func (w *WindowsShellHelper) GetShellType() *terminalcommon.TerminalShellType {
	w.mu.RLock()
	defer w.mu.RUnlock()
	return w.shellType
}

// GetShellTitle returns the current shell title
func (w *WindowsShellHelper) GetShellTitle() string {
	w.mu.RLock()
	defer w.mu.RUnlock()
	return w.shellTitle
}

// CheckShell checks the current shell state
func (w *WindowsShellHelper) CheckShell() {
	if runtime.GOOS != "windows" {
		return
	}

	w.updateShellInfo()
}

// startPolling starts polling for shell changes
func (w *WindowsShellHelper) startPolling() {
	w.mu.Lock()
	if w.polling {
		w.mu.Unlock()
		return
	}
	w.polling = true
	w.mu.Unlock()

	go w.pollShellInfo()
}

// pollShellInfo polls for shell information changes
func (w *WindowsShellHelper) pollShellInfo() {
	ticker := time.NewTicker(w.pollingInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			w.mu.RLock()
			if !w.polling {
				w.mu.RUnlock()
				return
			}
			w.mu.RUnlock()

			w.updateShellInfo()

		default:
			// Continue polling
		}
	}
}

// updateShellInfo updates shell information
func (w *WindowsShellHelper) updateShellInfo() {
	processName := w.getProcessName()
	if processName == "" {
		return
	}

	w.mu.Lock()
	oldShellType := w.shellType
	oldShellTitle := w.shellTitle

	// Update shell type based on process name
	newShellType := w.detectShellType(processName)
	w.shellType = &newShellType
	w.shellTitle = processName

	w.mu.Unlock()

	// Fire events if changed
	if oldShellType == nil || *oldShellType != newShellType {
		w.onShellTypeChanged.Fire(newShellType)
	}

	if oldShellTitle != processName {
		w.onShellNameChanged.Fire(processName)
	}
}

// getProcessName gets the process name for the PID
func (w *WindowsShellHelper) getProcessName() string {
	if runtime.GOOS != "windows" {
		return ""
	}

	// Use wmic to get process name on Windows
	cmd := exec.Command("wmic", "process", "where", "ProcessId="+strconv.Itoa(w.pid), "get", "Name", "/format:value")
	output, err := cmd.Output()
	if err != nil {
		return ""
	}

	lines := strings.Split(string(output), "\n")
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "Name=") {
			return strings.TrimPrefix(line, "Name=")
		}
	}

	return ""
}

// detectShellType detects the shell type from process name
func (w *WindowsShellHelper) detectShellType(processName string) terminalcommon.TerminalShellType {
	processName = strings.ToLower(processName)

	switch {
	case strings.Contains(processName, "powershell"):
		return terminalcommon.TerminalShellTypePowerShell
	case strings.Contains(processName, "pwsh"):
		return terminalcommon.TerminalShellTypePowerShell
	case strings.Contains(processName, "cmd"):
		return terminalcommon.TerminalShellTypeCmd
	case strings.Contains(processName, "bash"):
		return terminalcommon.TerminalShellTypeBash
	case strings.Contains(processName, "zsh"):
		return terminalcommon.TerminalShellTypeZsh
	case strings.Contains(processName, "fish"):
		return terminalcommon.TerminalShellTypeFish
	default:
		return terminalcommon.TerminalShellTypeUnknown
	}
}

// Dispose cleans up the helper
func (w *WindowsShellHelper) Dispose() {
	w.mu.Lock()
	w.polling = false
	w.mu.Unlock()

	w.Disposable.Dispose()
}
