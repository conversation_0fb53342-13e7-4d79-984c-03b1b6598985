/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"sync"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	instantiationcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/instantiation/common"
)

// UndoRedoElementType represents the type of undo/redo element
type UndoRedoElementType int

const (
	// UndoRedoElementTypeResource represents a resource-scoped element
	UndoRedoElementTypeResource UndoRedoElementType = iota
	// UndoRedoElementTypeWorkspace represents a workspace-scoped element
	UndoRedoElementTypeWorkspace
)

// IResourceUndoRedoElement represents a resource-scoped undo/redo element
type IResourceUndoRedoElement interface {
	// GetType returns the element type
	GetType() UndoRedoElementType
	// GetResource returns the resource impacted by this element
	GetResource() *basecommon.URI
	// GetLabel returns a user presentable label (may be localized)
	GetLabel() string
	// GetCode returns a code describing the operation (will not be localized)
	GetCode() string
	// GetConfirmBeforeUndo returns whether to show confirmation before undo
	GetConfirmBeforeUndo() bool
	// Undo performs the undo operation
	Undo() error
	// Redo performs the redo operation
	Redo() error
}

// IWorkspaceUndoRedoElement represents a workspace-scoped undo/redo element
type IWorkspaceUndoRedoElement interface {
	// GetType returns the element type
	GetType() UndoRedoElementType
	// GetResources returns the resources impacted by this element
	GetResources() []*basecommon.URI
	// GetLabel returns a user presentable label (may be localized)
	GetLabel() string
	// GetCode returns a code describing the operation (will not be localized)
	GetCode() string
	// GetConfirmBeforeUndo returns whether to show confirmation before undo
	GetConfirmBeforeUndo() bool
	// Undo performs the undo operation
	Undo() error
	// Redo performs the redo operation
	Redo() error
	// Split splits this element into multiple per-resource elements (optional)
	Split() ([]IResourceUndoRedoElement, error)
	// PrepareUndoRedo prepares for undo/redo operation (optional)
	PrepareUndoRedo() (basecommon.IDisposable, error)
}

// IUndoRedoElement represents either a resource or workspace undo/redo element
type IUndoRedoElement interface {
	// GetType returns the element type
	GetType() UndoRedoElementType
	// GetLabel returns a user presentable label (may be localized)
	GetLabel() string
	// GetCode returns a code describing the operation (will not be localized)
	GetCode() string
	// GetConfirmBeforeUndo returns whether to show confirmation before undo
	GetConfirmBeforeUndo() bool
	// Undo performs the undo operation
	Undo() error
	// Redo performs the redo operation
	Redo() error
}

// IPastFutureElements represents past and future elements
type IPastFutureElements struct {
	Past   []IUndoRedoElement `json:"past"`
	Future []IUndoRedoElement `json:"future"`
}

// UriComparisonKeyComputer computes comparison keys for URIs
type UriComparisonKeyComputer interface {
	GetComparisonKey(uri *basecommon.URI) string
}

// ResourceEditStackSnapshot represents a snapshot of a resource edit stack
type ResourceEditStackSnapshot struct {
	Resource *basecommon.URI `json:"resource"`
	Elements []int           `json:"elements"`
}

// NewResourceEditStackSnapshot creates a new ResourceEditStackSnapshot
func NewResourceEditStackSnapshot(resource *basecommon.URI, elements []int) *ResourceEditStackSnapshot {
	return &ResourceEditStackSnapshot{
		Resource: resource,
		Elements: elements,
	}
}

// UndoRedoGroup represents a group of related undo/redo operations
type UndoRedoGroup struct {
	ID    int `json:"id"`
	order int
	mutex sync.Mutex
}

var (
	undoRedoGroupIDCounter int
	undoRedoGroupMutex     sync.Mutex
)

// NewUndoRedoGroup creates a new UndoRedoGroup
func NewUndoRedoGroup() *UndoRedoGroup {
	undoRedoGroupMutex.Lock()
	defer undoRedoGroupMutex.Unlock()
	
	undoRedoGroupIDCounter++
	return &UndoRedoGroup{
		ID:    undoRedoGroupIDCounter,
		order: 1,
	}
}

// NextOrder returns the next order number for this group
func (g *UndoRedoGroup) NextOrder() int {
	g.mutex.Lock()
	defer g.mutex.Unlock()
	
	if g.ID == 0 {
		return 0
	}
	result := g.order
	g.order++
	return result
}

// UndoRedoGroupNone represents no group
var UndoRedoGroupNone = &UndoRedoGroup{ID: 0, order: 1}

// UndoRedoSource represents a source of undo/redo operations
type UndoRedoSource struct {
	ID    int `json:"id"`
	order int
	mutex sync.Mutex
}

var (
	undoRedoSourceIDCounter int
	undoRedoSourceMutex     sync.Mutex
)

// NewUndoRedoSource creates a new UndoRedoSource
func NewUndoRedoSource() *UndoRedoSource {
	undoRedoSourceMutex.Lock()
	defer undoRedoSourceMutex.Unlock()
	
	undoRedoSourceIDCounter++
	return &UndoRedoSource{
		ID:    undoRedoSourceIDCounter,
		order: 1,
	}
}

// NextOrder returns the next order number for this source
func (s *UndoRedoSource) NextOrder() int {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	if s.ID == 0 {
		return 0
	}
	result := s.order
	s.order++
	return result
}

// UndoRedoSourceNone represents no source
var UndoRedoSourceNone = &UndoRedoSource{ID: 0, order: 1}

// IUndoRedoService represents the undo/redo service interface
type IUndoRedoService interface {
	// ServiceBrand for type safety
	ServiceBrand() interface{}

	// RegisterUriComparisonKeyComputer registers an URI -> string hasher
	// This is useful for making multiple URIs share the same undo-redo stack
	RegisterUriComparisonKeyComputer(scheme string, uriComparisonKeyComputer UriComparisonKeyComputer) basecommon.IDisposable

	// GetUriComparisonKey gets the hash used internally for a certain URI
	// This uses any registered UriComparisonKeyComputer
	GetUriComparisonKey(resource *basecommon.URI) string

	// PushElement adds a new element to the undo stack
	// This will destroy the redo stack
	PushElement(element IUndoRedoElement, group *UndoRedoGroup, source *UndoRedoSource)

	// GetLastElement gets the last pushed element for a resource
	// If the last pushed element has been undone, returns nil
	GetLastElement(resource *basecommon.URI) IUndoRedoElement

	// GetElements gets all the elements associated with a resource
	// This includes the past and the future
	GetElements(resource *basecommon.URI) *IPastFutureElements

	// SetElementsValidFlag validates or invalidates stack elements associated with a resource
	SetElementsValidFlag(resource *basecommon.URI, isValid bool, filter func(element IUndoRedoElement) bool)

	// RemoveElements removes elements that target resource
	RemoveElements(resource interface{}) // Can be URI or string

	// CreateSnapshot creates a snapshot of the current elements on the undo-redo stack for a resource
	CreateSnapshot(resource *basecommon.URI) *ResourceEditStackSnapshot

	// RestoreSnapshot attempts to restore a certain snapshot previously created with CreateSnapshot for a resource
	RestoreSnapshot(snapshot *ResourceEditStackSnapshot)

	// CanUndo checks if undo is possible for the given resource or source
	CanUndo(resourceOrSource interface{}) bool // Can be URI or UndoRedoSource

	// Undo performs undo operation for the given resource or source
	Undo(resourceOrSource interface{}) error // Can be URI or UndoRedoSource

	// CanRedo checks if redo is possible for the given resource or source
	CanRedo(resourceOrSource interface{}) bool // Can be URI or UndoRedoSource

	// Redo performs redo operation for the given resource or source
	Redo(resourceOrSource interface{}) error // Can be URI or UndoRedoSource or string
}

// Service identifier for dependency injection
var IUndoRedoServiceID = instantiationcommon.CreateDecorator[IUndoRedoService]("undoRedoService")
