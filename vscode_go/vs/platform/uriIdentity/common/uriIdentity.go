/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"strings"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	instantiation "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/instantiation/common"
)

// IUriIdentityService provides URI identity and comparison services
type IUriIdentityService interface {
	// Service brand for type safety
	ServiceBrand() interface{}

	// ExtUri provides URI extensions that are aware of casing
	ExtUri() basecommon.IExtUri

	// AsCanonicalUri returns a canonical uri for the given resource
	AsCanonicalUri(uri *basecommon.URI) *basecommon.URI
}

// Use IExtUri from base/common/resources.go

// UriIdentityService implements IUriIdentityService
type UriIdentityService struct {
	extUri basecommon.IExtUri
}

// NewUriIdentityService creates a new URI identity service
func NewUriIdentityService(caseSensitive bool) *UriIdentityService {
	// Create a function that determines if path casing should be ignored
	ignorePathCasing := func(uri *basecommon.URI) bool {
		return !caseSensitive
	}

	return &UriIdentityService{
		extUri: basecommon.NewExtUri(ignorePathCasing),
	}
}

// ServiceBrand implements the service brand
func (uis *UriIdentityService) ServiceBrand() interface{} {
	return "uriIdentityService"
}

// ExtUri returns the extended URI operations
func (uis *UriIdentityService) ExtUri() basecommon.IExtUri {
	return uis.extUri
}

// AsCanonicalUri returns a canonical URI
func (uis *UriIdentityService) AsCanonicalUri(uri *basecommon.URI) *basecommon.URI {
	if uri == nil {
		return nil
	}

	// Normalize the URI path
	normalized := uri.With(basecommon.UriComponents{
		Scheme:    uri.Scheme,
		Authority: uri.Authority,
		Path:      normalizePath(uri.Path),
		Query:     uri.Query,
		Fragment:  uri.Fragment,
	})

	return normalized
}

// ExtUri implementation is now provided by base/common/resources.go

// Helper function to normalize paths
func normalizePath(path string) string {
	// Remove double slashes and resolve . and .. segments
	// This is a simplified implementation
	parts := strings.Split(path, "/")
	normalized := make([]string, 0, len(parts))

	for _, part := range parts {
		if part == "" || part == "." {
			continue
		}
		if part == ".." && len(normalized) > 0 {
			normalized = normalized[:len(normalized)-1]
		} else if part != ".." {
			normalized = append(normalized, part)
		}
	}

	result := "/" + strings.Join(normalized, "/")
	if result == "/" && path != "/" {
		return ""
	}
	return result
}

// Service identifier
var IUriIdentityServiceID = instantiation.CreateDecorator[IUriIdentityService]("uriIdentityService")
