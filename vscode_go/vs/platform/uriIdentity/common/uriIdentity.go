/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"strings"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	instantiation "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/instantiation/common"
)

// IUriIdentityService provides URI identity and comparison services
type IUriIdentityService interface {
	// Service brand for type safety
	ServiceBrand() interface{}

	// ExtUri provides URI extensions that are aware of casing
	ExtUri() IExtUri

	// AsCanonicalUri returns a canonical uri for the given resource
	AsCanonicalUri(uri *basecommon.URI) *basecommon.URI
}

// IExtUri provides extended URI operations
type IExtUri interface {
	// Compare compares two URIs
	Compare(uri1, uri2 *basecommon.URI) int

	// IsEqual checks if two URIs are equal
	IsEqual(uri1, uri2 *basecommon.URI) bool

	// IsEqualOrParent checks if uri1 is equal to or a parent of uri2
	IsEqualOrParent(uri1, uri2 *basecommon.URI) bool

	// IsAbsolutePath checks if the URI represents an absolute path
	IsAbsolutePath(uri *basecommon.URI) bool

	// Dirname returns the directory name of the URI
	Dirname(uri *basecommon.URI) *basecommon.URI

	// Basename returns the base name of the URI
	Basename(uri *basecommon.URI) string

	// JoinPath joins path segments to the URI
	JoinPath(uri *basecommon.URI, paths ...string) *basecommon.URI

	// RelativePath returns the relative path from base to target
	RelativePath(base, target *basecommon.URI) string

	// ResolvePath resolves a relative path against a base URI
	ResolvePath(base *basecommon.URI, path string) *basecommon.URI

	// GetComparisonKey creates a key for URI comparison
	GetComparisonKey(uri *basecommon.URI, ignoreFragment ...bool) string
}

// UriIdentityService implements IUriIdentityService
type UriIdentityService struct {
	extUri IExtUri
}

// NewUriIdentityService creates a new URI identity service
func NewUriIdentityService(caseSensitive bool) *UriIdentityService {
	return &UriIdentityService{
		extUri: NewExtUri(caseSensitive),
	}
}

// ServiceBrand implements the service brand
func (uis *UriIdentityService) ServiceBrand() interface{} {
	return "uriIdentityService"
}

// ExtUri returns the extended URI operations
func (uis *UriIdentityService) ExtUri() IExtUri {
	return uis.extUri
}

// AsCanonicalUri returns a canonical URI
func (uis *UriIdentityService) AsCanonicalUri(uri *basecommon.URI) *basecommon.URI {
	if uri == nil {
		return nil
	}

	// Normalize the URI path
	normalized := uri.With(basecommon.UriComponents{
		Scheme:    uri.Scheme,
		Authority: uri.Authority,
		Path:      normalizePath(uri.Path),
		Query:     uri.Query,
		Fragment:  uri.Fragment,
	})

	return normalized
}

// ExtUri implementation
type ExtUri struct {
	caseSensitive bool
}

// NewExtUri creates a new ExtUri instance
func NewExtUri(caseSensitive bool) *ExtUri {
	return &ExtUri{
		caseSensitive: caseSensitive,
	}
}

// Compare compares two URIs
func (eu *ExtUri) Compare(uri1, uri2 *basecommon.URI) int {
	if uri1 == nil && uri2 == nil {
		return 0
	}
	if uri1 == nil {
		return -1
	}
	if uri2 == nil {
		return 1
	}

	// Compare schemes
	if uri1.Scheme < uri2.Scheme {
		return -1
	}
	if uri1.Scheme > uri2.Scheme {
		return 1
	}

	// Compare authorities
	if uri1.Authority < uri2.Authority {
		return -1
	}
	if uri1.Authority > uri2.Authority {
		return 1
	}

	// Compare paths (case-sensitive or insensitive based on configuration)
	path1 := uri1.Path
	path2 := uri2.Path
	if !eu.caseSensitive {
		path1 = strings.ToLower(path1)
		path2 = strings.ToLower(path2)
	}

	if path1 < path2 {
		return -1
	}
	if path1 > path2 {
		return 1
	}

	return 0
}

// IsEqual checks if two URIs are equal
func (eu *ExtUri) IsEqual(uri1, uri2 *basecommon.URI) bool {
	return eu.Compare(uri1, uri2) == 0
}

// IsEqualOrParent checks if uri1 is equal to or a parent of uri2
func (eu *ExtUri) IsEqualOrParent(uri1, uri2 *basecommon.URI) bool {
	if eu.IsEqual(uri1, uri2) {
		return true
	}

	if uri1 == nil || uri2 == nil {
		return false
	}

	// Check if uri1 is a parent of uri2
	path1 := uri1.Path
	path2 := uri2.Path

	if !eu.caseSensitive {
		path1 = strings.ToLower(path1)
		path2 = strings.ToLower(path2)
	}

	// Ensure path1 ends with a separator
	if !strings.HasSuffix(path1, "/") {
		path1 += "/"
	}

	return strings.HasPrefix(path2, path1)
}

// IsAbsolutePath checks if the URI represents an absolute path
func (eu *ExtUri) IsAbsolutePath(uri *basecommon.URI) bool {
	if uri == nil {
		return false
	}
	return strings.HasPrefix(uri.Path, "/")
}

// Dirname returns the directory name of the URI
func (eu *ExtUri) Dirname(uri *basecommon.URI) *basecommon.URI {
	if uri == nil {
		return nil
	}

	path := uri.Path
	lastSlash := strings.LastIndex(path, "/")
	if lastSlash == -1 {
		return uri.With(basecommon.UriComponents{Path: ""})
	}

	dirPath := path[:lastSlash]
	return uri.With(basecommon.UriComponents{Path: dirPath})
}

// Basename returns the base name of the URI
func (eu *ExtUri) Basename(uri *basecommon.URI) string {
	if uri == nil {
		return ""
	}

	path := uri.Path
	lastSlash := strings.LastIndex(path, "/")
	if lastSlash == -1 {
		return path
	}

	return path[lastSlash+1:]
}

// JoinPath joins path segments to the URI
func (eu *ExtUri) JoinPath(uri *basecommon.URI, paths ...string) *basecommon.URI {
	if uri == nil {
		return nil
	}

	joinedPath := uri.Path
	for _, path := range paths {
		if !strings.HasSuffix(joinedPath, "/") && !strings.HasPrefix(path, "/") {
			joinedPath += "/"
		}
		joinedPath += path
	}

	return uri.With(basecommon.UriComponents{Path: joinedPath})
}

// RelativePath returns the relative path from base to target
func (eu *ExtUri) RelativePath(base, target *basecommon.URI) string {
	if base == nil || target == nil {
		return ""
	}

	// Simplified implementation
	basePath := base.Path
	targetPath := target.Path

	if strings.HasPrefix(targetPath, basePath) {
		relative := targetPath[len(basePath):]
		if strings.HasPrefix(relative, "/") {
			relative = relative[1:]
		}
		return relative
	}

	return targetPath
}

// ResolvePath resolves a relative path against a base URI
func (eu *ExtUri) ResolvePath(base *basecommon.URI, path string) *basecommon.URI {
	if base == nil {
		return nil
	}

	return eu.JoinPath(base, path)
}

// GetComparisonKey creates a key for URI comparison
func (eu *ExtUri) GetComparisonKey(uri *basecommon.URI, ignoreFragment ...bool) string {
	if uri == nil {
		return ""
	}

	ignoreFragmentFlag := false
	if len(ignoreFragment) > 0 {
		ignoreFragmentFlag = ignoreFragment[0]
	}

	// Create a copy of the URI for modification
	components := basecommon.UriComponents{
		Scheme:    uri.Scheme,
		Authority: uri.Authority,
		Path:      uri.Path,
		Query:     uri.Query,
		Fragment:  uri.Fragment,
	}

	// Apply case sensitivity to path
	if !eu.caseSensitive {
		components.Path = strings.ToLower(components.Path)
	}

	// Ignore fragment if requested
	if ignoreFragmentFlag {
		components.Fragment = ""
	}

	// Create URI with modified components and return string representation
	modifiedUri := basecommon.From(components)
	return modifiedUri.ToString()
}

// Helper function to normalize paths
func normalizePath(path string) string {
	// Remove double slashes and resolve . and .. segments
	// This is a simplified implementation
	parts := strings.Split(path, "/")
	normalized := make([]string, 0, len(parts))

	for _, part := range parts {
		if part == "" || part == "." {
			continue
		}
		if part == ".." && len(normalized) > 0 {
			normalized = normalized[:len(normalized)-1]
		} else if part != ".." {
			normalized = append(normalized, part)
		}
	}

	result := "/" + strings.Join(normalized, "/")
	if result == "/" && path != "/" {
		return ""
	}
	return result
}

// Service identifier
var IUriIdentityServiceID = instantiation.CreateDecorator[IUriIdentityService]("uriIdentityService")
