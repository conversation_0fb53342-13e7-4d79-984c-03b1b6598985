/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	baseCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	platformFiles "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/common"
	platformLog "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
	platformUriIdentity "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/uriIdentity/common"
	platformUserDataProfile "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/userDataProfile/common"
)

// FileUserDataProvider is a wrapper on top of the local filesystem provider which will:
//   - Convert the user data resources to file system scheme and vice-versa
//   - Enforces atomic reads for user data
type FileUserDataProvider struct {
	baseCommon.Disposable

	// Provider interfaces
	platformFiles.IFileSystemProviderWithFileReadWriteCapability
	platformFiles.IFileSystemProviderWithOpenReadWriteCloseCapability
	platformFiles.IFileSystemProviderWithFileReadStreamCapability
	platformFiles.IFileSystemProviderWithFileFolderCopyCapability
	platformFiles.IFileSystemProviderWithFileAtomicReadCapability
	platformFiles.IFileSystemProviderWithFileAtomicWriteCapability
	platformFiles.IFileSystemProviderWithFileAtomicDeleteCapability
	platformFiles.IFileSystemProviderWithFileCloneCapability

	// Properties
	capabilities            platformFiles.FileSystemProviderCapabilities
	onDidChangeCapabilities baseCommon.Event[interface{}]
	onDidChangeFile         baseCommon.Event[[]platformFiles.IFileChange]

	// Private fields
	fileSystemScheme        string
	fileSystemProvider      platformFiles.IFileSystemProvider
	userDataScheme          string
	userDataProfilesService platformUserDataProfile.IUserDataProfilesService
	uriIdentityService      platformUriIdentity.IUriIdentityService
	logService              platformLog.ILogService

	// Internal state
	_onDidChangeFile         *baseCommon.Emitter[[]platformFiles.IFileChange]
	watchResources           *baseCommon.TernarySearchTree[*baseCommon.URI]
	atomicReadWriteResources *baseCommon.ResourceSet
}

// NewFileUserDataProvider creates a new FileUserDataProvider
func NewFileUserDataProvider(
	fileSystemScheme string,
	fileSystemProvider platformFiles.IFileSystemProvider,
	userDataScheme string,
	userDataProfilesService platformUserDataProfile.IUserDataProfilesService,
	uriIdentityService platformUriIdentity.IUriIdentityService,
	logService platformLog.ILogService,
) *FileUserDataProvider {
	provider := &FileUserDataProvider{
		fileSystemScheme:        fileSystemScheme,
		fileSystemProvider:      fileSystemProvider,
		userDataScheme:          userDataScheme,
		userDataProfilesService: userDataProfilesService,
		uriIdentityService:      uriIdentityService,
		logService:              logService,
	}

	// Initialize capabilities
	provider.capabilities = fileSystemProvider.GetCapabilities()
	// Note: onDidChangeCapabilities will be handled through delegation

	// Initialize event emitter
	provider._onDidChangeFile = baseCommon.NewEmitter[[]platformFiles.IFileChange]()
	provider.onDidChangeFile = provider._onDidChangeFile.Event()

	// Initialize watch resources
	provider.watchResources = baseCommon.ForUris[*baseCommon.URI](func() bool {
		return (provider.capabilities & platformFiles.FileSystemProviderCapabilitiesPathCaseSensitive) == 0
	})

	// Initialize atomic read/write resources
	provider.atomicReadWriteResources = baseCommon.NewResourceSet(func(uri *baseCommon.URI) string {
		// Use a simple string representation for comparison
		return provider.toFileSystemResource(uri).ToString()
	})

	// Update atomic read/write resources
	provider.updateAtomicReadWritesResources()

	// Register event handlers
	provider.Register(userDataProfilesService.OnDidChangeProfiles().Subscribe(func(event *platformUserDataProfile.DidChangeProfilesEvent) {
		provider.updateAtomicReadWritesResources()
	}))

	provider.Register(fileSystemProvider.OnDidChangeFile().Subscribe(func(changes []platformFiles.IFileChange) {
		provider.handleFileChanges(changes)
	}))

	return provider
}

// GetCapabilities returns the provider capabilities
func (p *FileUserDataProvider) GetCapabilities() platformFiles.FileSystemProviderCapabilities {
	return p.capabilities
}

// OnDidChangeCapabilities returns the capabilities change event
func (p *FileUserDataProvider) OnDidChangeCapabilities() baseCommon.Event[interface{}] {
	return p.onDidChangeCapabilities
}

// OnDidChangeFile returns the file change event
func (p *FileUserDataProvider) OnDidChangeFile() baseCommon.Event[[]platformFiles.IFileChange] {
	return p.onDidChangeFile
}

// updateAtomicReadWritesResources updates the atomic read/write resources
func (p *FileUserDataProvider) updateAtomicReadWritesResources() {
	p.atomicReadWriteResources.Clear()
	for _, profile := range p.userDataProfilesService.Profiles() {
		p.atomicReadWriteResources.Add(profile.SettingsResource)
		p.atomicReadWriteResources.Add(profile.KeybindingsResource)
		p.atomicReadWriteResources.Add(profile.TasksResource)
		p.atomicReadWriteResources.Add(profile.ExtensionsResource)
	}
}

// Open opens a file and returns a file descriptor
func (p *FileUserDataProvider) Open(resource *baseCommon.URI, opts platformFiles.IFileOpenOptions) (int, error) {
	if provider, ok := p.fileSystemProvider.(platformFiles.IFileSystemProviderWithOpenReadWriteCloseCapability); ok {
		return provider.Open(p.toFileSystemResource(resource), opts)
	}
	return 0, baseCommon.NewError("Open not supported")
}

// Close closes a file descriptor
func (p *FileUserDataProvider) Close(fd int) error {
	if provider, ok := p.fileSystemProvider.(platformFiles.IFileSystemProviderWithOpenReadWriteCloseCapability); ok {
		return provider.Close(fd)
	}
	return baseCommon.NewError("Close not supported")
}

// Read reads from a file descriptor
func (p *FileUserDataProvider) Read(fd int, pos int64, data []byte, offset int, length int) (int, error) {
	if provider, ok := p.fileSystemProvider.(platformFiles.IFileSystemProviderWithOpenReadWriteCloseCapability); ok {
		return provider.Read(fd, pos, data, offset, length)
	}
	return 0, baseCommon.NewError("Read not supported")
}

// Write writes to a file descriptor
func (p *FileUserDataProvider) Write(fd int, pos int64, data []byte, offset int, length int) (int, error) {
	if provider, ok := p.fileSystemProvider.(platformFiles.IFileSystemProviderWithOpenReadWriteCloseCapability); ok {
		return provider.Write(fd, pos, data, offset, length)
	}
	return 0, baseCommon.NewError("Write not supported")
}

// Watch watches a resource for changes
func (p *FileUserDataProvider) Watch(resource *baseCommon.URI, opts platformFiles.IWatchOptions) baseCommon.IDisposable {
	p.watchResources.Set(resource.ToString(), resource)
	disposable := p.fileSystemProvider.Watch(p.toFileSystemResource(resource), opts)
	return baseCommon.ToDisposable(func() {
		p.watchResources.Delete(resource.ToString())
		disposable.Dispose()
	})
}

// Stat returns file statistics
func (p *FileUserDataProvider) Stat(resource *baseCommon.URI) (*platformFiles.IStat, error) {
	return p.fileSystemProvider.Stat(p.toFileSystemResource(resource))
}

// Mkdir creates a directory
func (p *FileUserDataProvider) Mkdir(resource *baseCommon.URI) error {
	return p.fileSystemProvider.Mkdir(p.toFileSystemResource(resource))
}

// Rename renames a file or directory
func (p *FileUserDataProvider) Rename(from, to *baseCommon.URI, opts platformFiles.IFileOverwriteOptions) error {
	return p.fileSystemProvider.Rename(p.toFileSystemResource(from), p.toFileSystemResource(to), opts)
}

// ReadFile reads a file
func (p *FileUserDataProvider) ReadFile(resource *baseCommon.URI, opts *platformFiles.IFileAtomicReadOptions) ([]byte, error) {
	if provider, ok := p.fileSystemProvider.(platformFiles.IFileSystemProviderWithFileAtomicReadCapability); ok {
		return provider.ReadFileAtomic(p.toFileSystemResource(resource), opts)
	}
	if provider, ok := p.fileSystemProvider.(platformFiles.IFileSystemProviderWithFileReadWriteCapability); ok {
		return provider.ReadFile(p.toFileSystemResource(resource))
	}
	return nil, baseCommon.NewError("ReadFile not supported")
}

// ReadFileStream reads a file as a stream
func (p *FileUserDataProvider) ReadFileStream(resource *baseCommon.URI, opts platformFiles.IFileReadStreamOptions, token baseCommon.CancellationToken) (baseCommon.ReadableStreamEvents[[]byte], error) {
	if provider, ok := p.fileSystemProvider.(platformFiles.IFileSystemProviderWithFileReadStreamCapability); ok {
		return provider.ReadFileStream(p.toFileSystemResource(resource), opts, token)
	}
	return nil, baseCommon.NewError("ReadFileStream not supported")
}

// Readdir reads a directory
func (p *FileUserDataProvider) Readdir(resource *baseCommon.URI) ([][2]interface{}, error) {
	return p.fileSystemProvider.Readdir(p.toFileSystemResource(resource))
}

// EnforceAtomicReadFile checks if atomic read should be enforced
func (p *FileUserDataProvider) EnforceAtomicReadFile(resource *baseCommon.URI) bool {
	return p.atomicReadWriteResources.Has(resource)
}

// WriteFile writes a file
func (p *FileUserDataProvider) WriteFile(resource *baseCommon.URI, content []byte, opts platformFiles.IFileWriteOptions) error {
	if provider, ok := p.fileSystemProvider.(platformFiles.IFileSystemProviderWithFileReadWriteCapability); ok {
		return provider.WriteFile(p.toFileSystemResource(resource), content, opts)
	}
	return baseCommon.NewError("WriteFile not supported")
}

// EnforceAtomicWriteFile checks if atomic write should be enforced
func (p *FileUserDataProvider) EnforceAtomicWriteFile(resource *baseCommon.URI) interface{} {
	if p.atomicReadWriteResources.Has(resource) {
		return map[string]string{"postfix": ".vsctmp"}
	}
	return false
}

// Delete deletes a file or directory
func (p *FileUserDataProvider) Delete(resource *baseCommon.URI, opts platformFiles.IFileDeleteOptions) error {
	return p.fileSystemProvider.Delete(p.toFileSystemResource(resource), opts)
}

// Copy copies a file or directory
func (p *FileUserDataProvider) Copy(from, to *baseCommon.URI, opts platformFiles.IFileOverwriteOptions) error {
	if platformFiles.HasFileFolderCopyCapability(p.fileSystemProvider) {
		if provider, ok := p.fileSystemProvider.(platformFiles.IFileSystemProviderWithFileFolderCopyCapability); ok {
			return provider.Copy(p.toFileSystemResource(from), p.toFileSystemResource(to), opts)
		}
	}
	return baseCommon.NewError("copy not supported")
}

// CloneFile clones a file
func (p *FileUserDataProvider) CloneFile(from, to *baseCommon.URI) error {
	if platformFiles.HasFileCloneCapability(p.fileSystemProvider) {
		if provider, ok := p.fileSystemProvider.(platformFiles.IFileSystemProviderWithFileCloneCapability); ok {
			return provider.CloneFile(p.toFileSystemResource(from), p.toFileSystemResource(to))
		}
	}
	return baseCommon.NewError("clone not supported")
}

// handleFileChanges handles file system changes
func (p *FileUserDataProvider) handleFileChanges(changes []platformFiles.IFileChange) {
	var userDataChanges []platformFiles.IFileChange
	for _, change := range changes {
		if change.Resource.Scheme != p.fileSystemScheme {
			continue // only interested in file schemes
		}

		userDataResource := p.toUserDataResource(change.Resource)
		if len(p.watchResources.FindSubstr(userDataResource.ToString())) > 0 {
			userDataChanges = append(userDataChanges, platformFiles.IFileChange{
				Resource: userDataResource,
				Type:     change.Type,
				CId:      change.CId,
			})
		}
	}
	if len(userDataChanges) > 0 {
		p.logService.Debug("User data changed")
		p._onDidChangeFile.Fire(userDataChanges)
	}
}

// toFileSystemResource converts a user data resource to a file system resource
func (p *FileUserDataProvider) toFileSystemResource(userDataResource *baseCommon.URI) *baseCommon.URI {
	return userDataResource.With(baseCommon.UriComponents{
		Scheme:    p.fileSystemScheme,
		Authority: userDataResource.Authority,
		Path:      userDataResource.Path,
		Query:     userDataResource.Query,
		Fragment:  userDataResource.Fragment,
	})
}

// toUserDataResource converts a file system resource to a user data resource
func (p *FileUserDataProvider) toUserDataResource(fileSystemResource *baseCommon.URI) *baseCommon.URI {
	return fileSystemResource.With(baseCommon.UriComponents{
		Scheme:    p.userDataScheme,
		Authority: fileSystemResource.Authority,
		Path:      fileSystemResource.Path,
		Query:     fileSystemResource.Query,
		Fragment:  fileSystemResource.Fragment,
	})
}
