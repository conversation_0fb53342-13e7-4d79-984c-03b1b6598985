/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	environmentcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/environment/common"
	filescommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/common"
	logcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
	statenode "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/state/node"
	uriidentitycommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/uriIdentity/common"
	userDataProfileCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/userDataProfile/common"
)

// StoredUserDataProfileState represents a stored user data profile state with location
type StoredUserDataProfileState struct {
	userDataProfileCommon.StoredUserDataProfile
	Location interface{} `json:"location"` // Can be URI or string
}

// UserDataProfilesReadonlyService extends BaseUserDataProfilesService and implements IUserDataProfilesService
type UserDataProfilesReadonlyService struct {
	*userDataProfileCommon.UserDataProfilesService

	stateReadonlyService     statenode.IStateReadService
	uriIdentityService       uriidentitycommon.IUriIdentityService
	nativeEnvironmentService environmentcommon.INativeEnvironmentService
	fileService              filescommon.IFileService
	logService               logcommon.ILogService
}

// Constants for UserDataProfilesReadonlyService
const (
	PROFILES_KEY             = "userDataProfiles"
	PROFILE_ASSOCIATIONS_KEY = "profileAssociations"
)

// NewUserDataProfilesReadonlyService creates a new UserDataProfilesReadonlyService
func NewUserDataProfilesReadonlyService(
	stateReadonlyService statenode.IStateReadService,
	uriIdentityService uriidentitycommon.IUriIdentityService,
	nativeEnvironmentService environmentcommon.INativeEnvironmentService,
	fileService filescommon.IFileService,
	logService logcommon.ILogService,
) *UserDataProfilesReadonlyService {
	base := userDataProfileCommon.NewUserDataProfilesService(
		nativeEnvironmentService,
		fileService,
		uriIdentityService,
		logService,
	)

	return &UserDataProfilesReadonlyService{
		UserDataProfilesService:  base,
		stateReadonlyService:     stateReadonlyService,
		uriIdentityService:       uriIdentityService,
		nativeEnvironmentService: nativeEnvironmentService,
		fileService:              fileService,
		logService:               logService,
	}
}

// GetStoredProfiles retrieves stored profiles from state service
func (ups *UserDataProfilesReadonlyService) GetStoredProfiles() []*userDataProfileCommon.StoredUserDataProfile {
	storedProfilesState := ups.stateReadonlyService.GetItem(PROFILES_KEY, []*StoredUserDataProfileState{})

	var storedProfiles []*userDataProfileCommon.StoredUserDataProfile

	if profiles, ok := storedProfilesState.([]*StoredUserDataProfileState); ok {
		storedProfiles = make([]*userDataProfileCommon.StoredUserDataProfile, len(profiles))
		for i, p := range profiles {
			var location *basecommon.URI

			// Handle location field - can be string or URI
			if p.Location != nil {
				switch loc := p.Location.(type) {
				case string:
					location = ups.uriIdentityService.ExtUri().JoinPath(ups.ProfilesHome(), loc)
				case *basecommon.URI:
					location = loc
				case map[string]interface{}:
					// Handle UriDto case - revive URI from components
					if scheme, ok := loc["scheme"].(string); ok {
						authority, _ := loc["authority"].(string)
						path, _ := loc["path"].(string)
						query, _ := loc["query"].(string)
						fragment, _ := loc["fragment"].(string)
						location = basecommon.NewURI(scheme, authority, path, query, fragment)
					}
				}
			}

			storedProfiles[i] = &userDataProfileCommon.StoredUserDataProfile{
				Name:            p.Name,
				Location:        location,
				Icon:            p.Icon,
				UseDefaultFlags: p.UseDefaultFlags,
			}
		}
	}

	return storedProfiles
}

// GetStoredProfileAssociations retrieves stored profile associations from state service
func (ups *UserDataProfilesReadonlyService) GetStoredProfileAssociations() *userDataProfileCommon.StoredProfileAssociations {
	storedAssociations := ups.stateReadonlyService.GetItem(PROFILE_ASSOCIATIONS_KEY, &userDataProfileCommon.StoredProfileAssociations{})

	if associations, ok := storedAssociations.(*userDataProfileCommon.StoredProfileAssociations); ok {
		return associations
	}

	return &userDataProfileCommon.StoredProfileAssociations{}
}

// GetDefaultProfileExtensionsLocation returns the default profile extensions location
func (ups *UserDataProfilesReadonlyService) GetDefaultProfileExtensionsLocation() *basecommon.URI {
	extensionsPath := ups.nativeEnvironmentService.ExtensionsPath()
	extensionsURI := basecommon.NewURI("file", "", extensionsPath, "", "")

	// Convert to the same scheme as profiles home
	profilesHome := ups.ProfilesHome()
	extensionsURIWithScheme := extensionsURI.With(basecommon.UriComponents{
		Scheme: profilesHome.Scheme,
	})

	return ups.uriIdentityService.ExtUri().JoinPath(extensionsURIWithScheme, "extensions.json")
}

// ProfilesKey returns the profiles key used for state storage
func (ups *UserDataProfilesReadonlyService) ProfilesKey() string {
	return PROFILES_KEY
}

// ProfileAssociationsKey returns the profile associations key used for state storage
func (ups *UserDataProfilesReadonlyService) ProfileAssociationsKey() string {
	return PROFILE_ASSOCIATIONS_KEY
}

// UserDataProfilesService extends UserDataProfilesReadonlyService and implements IUserDataProfilesService
type UserDataProfilesService struct {
	*UserDataProfilesReadonlyService

	stateService statenode.IStateService
}

// NewUserDataProfilesService creates a new UserDataProfilesService
func NewUserDataProfilesService(
	stateService statenode.IStateService,
	uriIdentityService uriidentitycommon.IUriIdentityService,
	environmentService environmentcommon.INativeEnvironmentService,
	fileService filescommon.IFileService,
	logService logcommon.ILogService,
) *UserDataProfilesService {
	readonlyService := NewUserDataProfilesReadonlyService(
		stateService,
		uriIdentityService,
		environmentService,
		fileService,
		logService,
	)

	return &UserDataProfilesService{
		UserDataProfilesReadonlyService: readonlyService,
		stateService:                    stateService,
	}
}

// SaveStoredProfiles saves stored profiles to state service
func (ups *UserDataProfilesService) SaveStoredProfiles(storedProfiles []*userDataProfileCommon.StoredUserDataProfile) {
	if len(storedProfiles) > 0 {
		// Convert profiles to storage format with relative paths
		storageProfiles := make([]*userDataProfileCommon.StoredUserDataProfile, len(storedProfiles))
		for i, profile := range storedProfiles {
			var location *basecommon.URI
			if profile.Location != nil {
				// Convert to relative path for storage
				basename := ups.uriIdentityService.ExtUri().Basename(profile.Location)
				location = basecommon.NewURI("", "", basename, "", "")
			}

			storageProfiles[i] = &userDataProfileCommon.StoredUserDataProfile{
				Name:            profile.Name,
				Location:        location,
				Icon:            profile.Icon,
				UseDefaultFlags: profile.UseDefaultFlags,
			}
		}

		ups.stateService.SetItem(ups.ProfilesKey(), storageProfiles)
	} else {
		ups.stateService.RemoveItem(ups.ProfilesKey())
	}
}

// SaveStoredProfileAssociations saves stored profile associations to state service
func (ups *UserDataProfilesService) SaveStoredProfileAssociations(storedProfileAssociations *userDataProfileCommon.StoredProfileAssociations) {
	if storedProfileAssociations != nil &&
		(storedProfileAssociations.EmptyWindows != nil || storedProfileAssociations.Workspaces != nil) {
		ups.stateService.SetItem(ups.ProfileAssociationsKey(), storedProfileAssociations)
	} else {
		ups.stateService.RemoveItem(ups.ProfileAssociationsKey())
	}
}

// ServerUserDataProfilesService extends UserDataProfilesService for server scenarios
type ServerUserDataProfilesService struct {
	*UserDataProfilesService

	internalStateService *statenode.StateService
}

// NewServerUserDataProfilesService creates a new ServerUserDataProfilesService
func NewServerUserDataProfilesService(
	uriIdentityService uriidentitycommon.IUriIdentityService,
	environmentService environmentcommon.INativeEnvironmentService,
	fileService filescommon.IFileService,
	logService logcommon.ILogService,
) *ServerUserDataProfilesService {
	stateService := statenode.NewStateService(
		statenode.SaveStrategyImmediate,
		environmentService,
		logService,
		fileService,
	)

	userDataProfilesService := NewUserDataProfilesService(
		stateService,
		uriIdentityService,
		environmentService,
		fileService,
		logService,
	)

	return &ServerUserDataProfilesService{
		UserDataProfilesService: userDataProfilesService,
		internalStateService:    stateService,
	}
}

// Init initializes the server user data profiles service
func (sups *ServerUserDataProfilesService) Init() error {
	// Initialize the internal state service first
	if err := sups.internalStateService.Init(); err != nil {
		return err
	}

	// Then initialize the base user data profiles service
	sups.UserDataProfilesService.UserDataProfilesReadonlyService.UserDataProfilesService.Init()

	return nil
}

// Close closes the server user data profiles service
func (sups *ServerUserDataProfilesService) Close() error {
	if sups.internalStateService != nil {
		return sups.internalStateService.Close()
	}
	return nil
}

// Helper function to revive URI from map
func reviveURI(uriMap map[string]interface{}) *basecommon.URI {
	scheme, _ := uriMap["scheme"].(string)
	authority, _ := uriMap["authority"].(string)
	path, _ := uriMap["path"].(string)
	query, _ := uriMap["query"].(string)
	fragment, _ := uriMap["fragment"].(string)

	return basecommon.From(basecommon.UriComponents{
		Scheme:    scheme,
		Authority: authority,
		Path:      path,
		Query:     query,
		Fragment:  fragment,
	})
}
