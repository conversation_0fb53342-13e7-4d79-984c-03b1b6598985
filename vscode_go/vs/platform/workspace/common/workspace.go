/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"path/filepath"
	"strings"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	environmentcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/environment/common"
)

// WorkbenchState represents the state of the workbench
type WorkbenchState int

const (
	WorkbenchStateEmpty WorkbenchState = iota + 1
	WorkbenchStateFolder
	WorkbenchStateWorkspace
)

// String returns the string representation of WorkbenchState
func (w WorkbenchState) String() string {
	switch w {
	case WorkbenchStateEmpty:
		return "EMPTY"
	case WorkbenchStateFolder:
		return "FOLDER"
	case WorkbenchStateWorkspace:
		return "WORKSPACE"
	default:
		return "UNKNOWN"
	}
}

// IWorkspaceContextService represents the workspace context service interface
type IWorkspaceContextService interface {
	// Service brand for type safety
	GetServiceBrand() interface{}

	// Event subscriptions
	OnDidChangeWorkbenchState() basecommon.Event[WorkbenchState]
	OnDidChangeWorkspaceName() basecommon.Event[interface{}]
	OnWillChangeWorkspaceFolders() basecommon.Event[*IWorkspaceFoldersWillChangeEvent]
	OnDidChangeWorkspaceFolders() basecommon.Event[*IWorkspaceFoldersChangeEvent]

	// Workspace access methods
	GetCompleteWorkspace() (*IWorkspace, error)
	GetWorkspace() *IWorkspace
	GetWorkbenchState() WorkbenchState
	GetWorkspaceFolder(resource *basecommon.URI) *IWorkspaceFolder
	IsCurrentWorkspace(workspaceIdOrFolder interface{}) bool
	IsInsideWorkspace(resource *basecommon.URI) bool
}

// IBaseWorkspaceIdentifier represents the base workspace identifier
type IBaseWorkspaceIdentifier struct {
	// Every workspace (multi-root, single folder or empty)
	// has a unique identifier. It is not possible to open
	// a workspace with the same `id` in multiple windows
	ID string `json:"id"`
}

// ISingleFolderWorkspaceIdentifier represents a single folder workspace identifier
type ISingleFolderWorkspaceIdentifier struct {
	IBaseWorkspaceIdentifier
	// Folder path as URI
	URI *basecommon.URI `json:"uri"`
}

// IWorkspaceIdentifier represents a multi-root workspace identifier
type IWorkspaceIdentifier struct {
	IBaseWorkspaceIdentifier
	// Workspace config file path as URI
	ConfigPath *basecommon.URI `json:"configPath"`
}

// IEmptyWorkspaceIdentifier represents an empty workspace identifier
type IEmptyWorkspaceIdentifier struct {
	IBaseWorkspaceIdentifier
}

// IAnyWorkspaceIdentifier represents any type of workspace identifier
type IAnyWorkspaceIdentifier interface {
	GetID() string
}

// GetID implements IAnyWorkspaceIdentifier for ISingleFolderWorkspaceIdentifier
func (s *ISingleFolderWorkspaceIdentifier) GetID() string {
	return s.ID
}

// GetID implements IAnyWorkspaceIdentifier for IWorkspaceIdentifier
func (w *IWorkspaceIdentifier) GetID() string {
	return w.ID
}

// GetID implements IAnyWorkspaceIdentifier for IEmptyWorkspaceIdentifier
func (e *IEmptyWorkspaceIdentifier) GetID() string {
	return e.ID
}

// Predefined workspace identifiers
var (
	ExtensionDevelopmentEmptyWindowWorkspace = &IEmptyWorkspaceIdentifier{
		IBaseWorkspaceIdentifier: IBaseWorkspaceIdentifier{ID: "ext-dev"},
	}
	UnknownEmptyWindowWorkspace = &IEmptyWorkspaceIdentifier{
		IBaseWorkspaceIdentifier: IBaseWorkspaceIdentifier{ID: "empty-window"},
	}
	StandaloneEditorWorkspaceID = "4064f6ec-cb38-4ad0-af64-ee6467e63c82"
)

// IBaseWorkspace represents the base workspace interface
type IBaseWorkspace struct {
	// If present, marks the window that opens the workspace
	// as a remote window with the given authority.
	RemoteAuthority *string `json:"remoteAuthority,omitempty"`

	// Transient workspaces are meant to go away after being used
	// once, e.g. a window reload of a transient workspace will
	// open an empty window.
	Transient *bool `json:"transient,omitempty"`
}

// IWorkspace represents a workspace
type IWorkspace struct {
	// The unique identifier of the workspace
	ID string `json:"id"`

	// Folders in the workspace
	Folders []*IWorkspaceFolder `json:"folders"`

	// Transient workspaces are meant to go away after being used once
	Transient *bool `json:"transient,omitempty"`

	// The location of the workspace configuration
	Configuration *basecommon.URI `json:"configuration,omitempty"`
}

// IResolvedWorkspace represents a resolved workspace
type IResolvedWorkspace struct {
	IWorkspaceIdentifier
	IBaseWorkspace
	Folders []*IWorkspaceFolder `json:"folders"`
}

// IWorkspaceFolderData represents workspace folder data
type IWorkspaceFolderData struct {
	// The associated URI for this workspace folder
	URI *basecommon.URI `json:"uri"`

	// The name of this workspace folder. Defaults to
	// the basename of its uri-path
	Name string `json:"name"`

	// The ordinal number of this workspace folder
	Index int `json:"index"`
}

// IWorkspaceFolder represents a workspace folder
type IWorkspaceFolder struct {
	IWorkspaceFolderData

	// Given workspace folder relative path, returns the resource with the absolute path
	ToResource func(relativePath string) *basecommon.URI `json:"-"`
}

// IWorkspaceFoldersWillChangeEvent represents an event before workspace folders change
type IWorkspaceFoldersWillChangeEvent struct {
	Changes   *IWorkspaceFoldersChangeEvent `json:"changes"`
	FromCache bool                          `json:"fromCache"`

	// Join allows waiting for a promise
	Join func(promise chan error) `json:"-"`
}

// IWorkspaceFoldersChangeEvent represents workspace folders change event
type IWorkspaceFoldersChangeEvent struct {
	Added   []*IWorkspaceFolder `json:"added"`
	Removed []*IWorkspaceFolder `json:"removed"`
	Changed []*IWorkspaceFolder `json:"changed"`
}

// IRawFileWorkspaceFolder represents raw file workspace folder
type IRawFileWorkspaceFolder struct {
	Path string  `json:"path"`
	Name *string `json:"name,omitempty"`
}

// IRawUriWorkspaceFolder represents raw URI workspace folder
type IRawUriWorkspaceFolder struct {
	URI  string  `json:"uri"`
	Name *string `json:"name,omitempty"`
}

// Workspace constants
const (
	WorkspaceExtension    = "code-workspace"
	WorkspaceSuffix       = "." + WorkspaceExtension
	UntitledWorkspaceName = "workspace.json"
)

// Type checking functions

// IsSingleFolderWorkspaceIdentifier checks if obj is a single folder workspace identifier
func IsSingleFolderWorkspaceIdentifier(obj interface{}) bool {
	if identifier, ok := obj.(*ISingleFolderWorkspaceIdentifier); ok {
		return identifier.ID != "" && identifier.URI != nil
	}
	return false
}

// IsWorkspaceIdentifier checks if obj is a workspace identifier
func IsWorkspaceIdentifier(obj interface{}) bool {
	if identifier, ok := obj.(*IWorkspaceIdentifier); ok {
		return identifier.ID != "" && identifier.ConfigPath != nil
	}
	return false
}

// IsEmptyWorkspaceIdentifier checks if obj is an empty workspace identifier
func IsEmptyWorkspaceIdentifier(obj interface{}) bool {
	if identifier, ok := obj.(*IEmptyWorkspaceIdentifier); ok {
		return identifier.ID != "" &&
			!IsSingleFolderWorkspaceIdentifier(obj) &&
			!IsWorkspaceIdentifier(obj)
	}
	return false
}

// IsWorkspace checks if thing is a workspace
func IsWorkspace(thing interface{}) bool {
	if workspace, ok := thing.(*IWorkspace); ok {
		return workspace.ID != "" && workspace.Folders != nil
	}
	return false
}

// IsWorkspaceFolder checks if thing is a workspace folder
func IsWorkspaceFolder(thing interface{}) bool {
	if folder, ok := thing.(*IWorkspaceFolder); ok {
		return folder.URI != nil && folder.Name != "" && folder.ToResource != nil
	}
	return false
}

// IsStandaloneEditorWorkspace checks if workspace is a standalone editor workspace
func IsStandaloneEditorWorkspace(workspace *IWorkspace) bool {
	return workspace.ID == StandaloneEditorWorkspaceID
}

// HasWorkspaceFileExtension checks if path has workspace file extension
func HasWorkspaceFileExtension(path interface{}) bool {
	var ext string
	switch p := path.(type) {
	case string:
		ext = filepath.Ext(p)
	case *basecommon.URI:
		// Create ExtUri instance to get extension
		extUri := &basecommon.ExtUri{}
		ext = extUri.Extname(p)
	default:
		return false
	}
	return ext == WorkspaceSuffix
}

// IsUntitledWorkspace checks if path is an untitled workspace
func IsUntitledWorkspace(path *basecommon.URI, environmentService environmentcommon.IEnvironmentService) bool {
	untitledHome := environmentService.UntitledWorkspacesHome()
	extUri := &basecommon.ExtUri{}
	return extUri.IsEqualOrParent(path, untitledHome, true)
}

// IsTemporaryWorkspace checks if workspace or path is temporary
func IsTemporaryWorkspace(arg interface{}) bool {
	var path *basecommon.URI

	switch v := arg.(type) {
	case *basecommon.URI:
		path = v
	case *IWorkspace:
		path = v.Configuration
	default:
		return false
	}

	if path == nil {
		return false
	}

	return path.Scheme == basecommon.Schemas.Tmp
}

// IsSavedWorkspace checks if path is a saved workspace
func IsSavedWorkspace(path *basecommon.URI, environmentService environmentcommon.IEnvironmentService) bool {
	return !IsUntitledWorkspace(path, environmentService) && !IsTemporaryWorkspace(path)
}

// Workspace represents a workspace implementation
type Workspace struct {
	id               string
	folders          []*WorkspaceFolder
	transient        bool
	configuration    *basecommon.URI
	ignorePathCasing func(*basecommon.URI) bool
	// Use a simple map instead of ternary search tree to avoid stack overflow
	foldersMap map[string]*WorkspaceFolder
}

// NewWorkspace creates a new workspace
func NewWorkspace(
	id string,
	folders []*WorkspaceFolder,
	transient bool,
	configuration *basecommon.URI,
	ignorePathCasing func(*basecommon.URI) bool,
) *Workspace {
	w := &Workspace{
		id:               id,
		transient:        transient,
		configuration:    configuration,
		ignorePathCasing: ignorePathCasing,
		foldersMap:       make(map[string]*WorkspaceFolder),
	}
	w.SetFolders(folders)
	return w
}

// GetID returns the workspace ID
func (w *Workspace) GetID() string {
	return w.id
}

// GetFolders returns the workspace folders
func (w *Workspace) GetFolders() []*WorkspaceFolder {
	return w.folders
}

// SetFolders sets the workspace folders and updates the folders map
func (w *Workspace) SetFolders(folders []*WorkspaceFolder) {
	w.folders = folders
	w.updateFoldersMap()
}

// IsTransient returns whether the workspace is transient
func (w *Workspace) IsTransient() bool {
	return w.transient
}

// GetConfiguration returns the workspace configuration URI
func (w *Workspace) GetConfiguration() *basecommon.URI {
	return w.configuration
}

// SetConfiguration sets the workspace configuration URI
func (w *Workspace) SetConfiguration(configuration *basecommon.URI) {
	w.configuration = configuration
}

// GetFolder returns the workspace folder for the given resource
func (w *Workspace) GetFolder(resource *basecommon.URI) *IWorkspaceFolder {
	if resource == nil {
		return nil
	}

	resourcePath := resource.String()

	// Find the folder that contains this resource (simple prefix matching)
	var bestMatch *WorkspaceFolder
	var bestMatchLength int

	for _, folder := range w.folders {
		folderPath := folder.URI.String()
		if strings.HasPrefix(resourcePath, folderPath) {
			if len(folderPath) > bestMatchLength {
				bestMatch = folder
				bestMatchLength = len(folderPath)
			}
		}
	}

	if bestMatch == nil {
		return nil
	}

	return &IWorkspaceFolder{
		IWorkspaceFolderData: IWorkspaceFolderData{
			URI:   bestMatch.URI,
			Name:  bestMatch.Name,
			Index: bestMatch.Index,
		},
		ToResource: bestMatch.ToResource,
	}
}

// Update updates the workspace with another workspace's data
func (w *Workspace) Update(workspace *Workspace) {
	w.id = workspace.id
	w.configuration = workspace.configuration
	w.transient = workspace.transient
	w.ignorePathCasing = workspace.ignorePathCasing
	w.SetFolders(workspace.folders)
}

// ToJSON converts the workspace to JSON-serializable format
func (w *Workspace) ToJSON() *IWorkspace {
	folders := make([]*IWorkspaceFolder, len(w.folders))
	for i, folder := range w.folders {
		folders[i] = &IWorkspaceFolder{
			IWorkspaceFolderData: IWorkspaceFolderData{
				URI:   folder.URI,
				Name:  folder.Name,
				Index: folder.Index,
			},
			ToResource: folder.ToResource,
		}
	}

	var transient *bool
	if w.transient {
		transient = &w.transient
	}

	return &IWorkspace{
		ID:            w.id,
		Folders:       folders,
		Transient:     transient,
		Configuration: w.configuration,
	}
}

// updateFoldersMap updates the internal folders map
func (w *Workspace) updateFoldersMap() {
	w.foldersMap = make(map[string]*WorkspaceFolder)
	for _, folder := range w.folders {
		w.foldersMap[folder.URI.String()] = folder
	}
}

// WorkspaceFolder represents a workspace folder implementation
type WorkspaceFolder struct {
	URI        *basecommon.URI
	Name       string
	Index      int
	Raw        interface{} // IRawFileWorkspaceFolder or IRawUriWorkspaceFolder
	ToResource func(relativePath string) *basecommon.URI
}

// NewWorkspaceFolder creates a new workspace folder
func NewWorkspaceFolder(data *IWorkspaceFolderData, raw interface{}) *WorkspaceFolder {
	folder := &WorkspaceFolder{
		URI:   data.URI,
		Name:  data.Name,
		Index: data.Index,
		Raw:   raw,
	}

	// Set up ToResource function
	folder.ToResource = func(relativePath string) *basecommon.URI {
		extUri := &basecommon.ExtUri{}
		return extUri.JoinPath(folder.URI, relativePath)
	}

	return folder
}

// ToJSON converts the workspace folder to JSON-serializable format
func (wf *WorkspaceFolder) ToJSON() *IWorkspaceFolderData {
	return &IWorkspaceFolderData{
		URI:   wf.URI,
		Name:  wf.Name,
		Index: wf.Index,
	}
}

// ToWorkspaceFolder creates a workspace folder from a resource URI
func ToWorkspaceFolder(resource *basecommon.URI) *WorkspaceFolder {
	extUri := &basecommon.ExtUri{}
	name := extUri.BasenameOrAuthority(resource)
	data := &IWorkspaceFolderData{
		URI:   resource,
		Index: 0,
		Name:  name,
	}
	raw := &IRawUriWorkspaceFolder{
		URI: resource.String(),
	}
	return NewWorkspaceFolder(data, raw)
}

// ToWorkspaceIdentifier converts workspace or backup path to workspace identifier
func ToWorkspaceIdentifier(arg interface{}, isExtensionDevelopment ...bool) IAnyWorkspaceIdentifier {
	// Handle string or nil (empty workspace)
	if backupPath, ok := arg.(string); ok || arg == nil {
		// With a backupPath, the basename is the empty workspace identifier
		if backupPath != "" {
			return &IEmptyWorkspaceIdentifier{
				IBaseWorkspaceIdentifier: IBaseWorkspaceIdentifier{
					ID: filepath.Base(backupPath),
				},
			}
		}

		// Extension development empty windows have backups disabled
		// so we return a constant workspace identifier for extension
		// authors to allow to restore their workspace state even then.
		if len(isExtensionDevelopment) > 0 && isExtensionDevelopment[0] {
			return ExtensionDevelopmentEmptyWindowWorkspace
		}

		return UnknownEmptyWindowWorkspace
	}

	// Handle workspace
	if workspace, ok := arg.(*IWorkspace); ok {
		// Multi root
		if workspace.Configuration != nil {
			return &IWorkspaceIdentifier{
				IBaseWorkspaceIdentifier: IBaseWorkspaceIdentifier{ID: workspace.ID},
				ConfigPath:               workspace.Configuration,
			}
		}

		// Single folder
		if len(workspace.Folders) == 1 {
			return &ISingleFolderWorkspaceIdentifier{
				IBaseWorkspaceIdentifier: IBaseWorkspaceIdentifier{ID: workspace.ID},
				URI:                      workspace.Folders[0].URI,
			}
		}

		// Empty window
		return &IEmptyWorkspaceIdentifier{
			IBaseWorkspaceIdentifier: IBaseWorkspaceIdentifier{ID: workspace.ID},
		}
	}

	// Default to unknown empty workspace
	return UnknownEmptyWindowWorkspace
}

// ISerializedSingleFolderWorkspaceIdentifier represents serialized single folder workspace identifier
type ISerializedSingleFolderWorkspaceIdentifier struct {
	IBaseWorkspaceIdentifier
	URI *basecommon.UriComponents `json:"uri"`
}

// ISerializedWorkspaceIdentifier represents serialized workspace identifier
type ISerializedWorkspaceIdentifier struct {
	IBaseWorkspaceIdentifier
	ConfigPath *basecommon.UriComponents `json:"configPath"`
}

// ReviveIdentifier revives a serialized workspace identifier
func ReviveIdentifier(identifier interface{}) IAnyWorkspaceIdentifier {
	if identifier == nil {
		return nil
	}

	// Try single folder identifier
	if singleFolder, ok := identifier.(*ISerializedSingleFolderWorkspaceIdentifier); ok && singleFolder.URI != nil {
		return &ISingleFolderWorkspaceIdentifier{
			IBaseWorkspaceIdentifier: IBaseWorkspaceIdentifier{ID: singleFolder.ID},
			URI:                      basecommon.From(*singleFolder.URI),
		}
	}

	// Try multi folder identifier
	if workspace, ok := identifier.(*ISerializedWorkspaceIdentifier); ok && workspace.ConfigPath != nil {
		return &IWorkspaceIdentifier{
			IBaseWorkspaceIdentifier: IBaseWorkspaceIdentifier{ID: workspace.ID},
			ConfigPath:               basecommon.From(*workspace.ConfigPath),
		}
	}

	// Try empty identifier
	if empty, ok := identifier.(*IEmptyWorkspaceIdentifier); ok && empty.ID != "" {
		return &IEmptyWorkspaceIdentifier{
			IBaseWorkspaceIdentifier: IBaseWorkspaceIdentifier{ID: empty.ID},
		}
	}

	// Try base identifier with ID
	if base, ok := identifier.(map[string]interface{}); ok {
		if id, exists := base["id"].(string); exists && id != "" {
			return &IEmptyWorkspaceIdentifier{
				IBaseWorkspaceIdentifier: IBaseWorkspaceIdentifier{ID: id},
			}
		}
	}

	return nil
}

// WorkspaceFilter represents workspace file filter for dialogs
var WorkspaceFilter = []map[string]interface{}{
	{
		"name":       "Code Workspace", // This would be localized in real implementation
		"extensions": []string{WorkspaceExtension},
	},
}
