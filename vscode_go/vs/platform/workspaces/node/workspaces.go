/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"crypto/md5"
	"fmt"
	"math/rand"
	"os"
	"strconv"
	"strings"
	"time"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	workspacecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/workspace/common"
)

// NON_EMPTY_WORKSPACE_ID_LENGTH is the length of workspace identifiers that are not empty.
// Those are MD5 hashes (128bits / 4 due to hex presentation).
const NON_EMPTY_WORKSPACE_ID_LENGTH = 128 / 4

// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
// NOTE: DO NOT CHANGE. IDENTIFIERS HAVE TO REMAIN STABLE
// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

// GetWorkspaceIdentifier creates a workspace identifier from a config path
func GetWorkspaceIdentifier(configPath *basecommon.URI) *workspacecommon.IWorkspaceIdentifier {
	getWorkspaceId := func() string {
		var configPathStr string
		if configPath.Scheme == basecommon.Schemas.File {
			configPathStr = basecommon.OriginalFSPath(configPath)
		} else {
			configPathStr = configPath.ToString()
		}

		if !basecommon.IsLinux {
			configPathStr = strings.ToLower(configPathStr) // sanitize for platform file system
		}

		hash := md5.Sum([]byte(configPathStr))
		return fmt.Sprintf("%x", hash) // CodeQL [SM04514] Using MD5 to convert a file path to a fixed length
	}

	return &workspacecommon.IWorkspaceIdentifier{
		IBaseWorkspaceIdentifier: workspacecommon.IBaseWorkspaceIdentifier{
			ID: getWorkspaceId(),
		},
		ConfigPath: configPath,
	}
}

// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
// NOTE: DO NOT CHANGE. IDENTIFIERS HAVE TO REMAIN STABLE
// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

// GetSingleFolderWorkspaceIdentifier creates a single folder workspace identifier
func GetSingleFolderWorkspaceIdentifier(folderUri *basecommon.URI, folderStat *os.FileInfo) *workspacecommon.ISingleFolderWorkspaceIdentifier {
	getFolderId := func() *string {
		// Remote: produce a hash from the entire URI
		if folderUri.Scheme != basecommon.Schemas.File {
			hash := md5.Sum([]byte(folderUri.ToString()))
			result := fmt.Sprintf("%x", hash) // CodeQL [SM04514] Using MD5 to convert a file path to a fixed length
			return &result
		}

		// Local: we use the ctime as extra salt to the
		// identifier so that folders getting recreated
		// result in a different identifier. However, if
		// the stat is not provided we return nil
		// to ensure identifiers are stable for the given
		// URI.
		if folderStat == nil {
			return nil
		}

		var ctime int64
		if basecommon.IsLinux {
			// Linux: birthtime is ctime, so we cannot use it! We use the sys info instead!
			// In Go, we'll use ModTime as a fallback since we can't easily get inode info
			// without platform-specific code. This is a limitation of the Go port.
			ctime = (*folderStat).ModTime().UnixNano()
		} else if basecommon.IsMacintosh {
			// macOS: we'll use ModTime as a reasonable approximation
			ctime = (*folderStat).ModTime().UnixNano()
		} else if basecommon.IsWindows {
			// Windows: we'll use ModTime as a reasonable approximation
			ctime = (*folderStat).ModTime().UnixNano()
		}

		ctimeStr := ""
		if ctime != 0 {
			ctimeStr = strconv.FormatInt(ctime, 10)
		}

		hash := md5.Sum([]byte(folderUri.FSPath() + ctimeStr))
		result := fmt.Sprintf("%x", hash) // CodeQL [SM04514] Using MD5 to convert a file path to a fixed length
		return &result
	}

	folderId := getFolderId()
	if folderId != nil {
		return &workspacecommon.ISingleFolderWorkspaceIdentifier{
			IBaseWorkspaceIdentifier: workspacecommon.IBaseWorkspaceIdentifier{
				ID: *folderId,
			},
			URI: folderUri,
		}
	}

	return nil // invalid folder
}

// GetSingleFolderWorkspaceIdentifierWithoutStat creates a single folder workspace identifier without stat
func GetSingleFolderWorkspaceIdentifierWithoutStat(folderUri *basecommon.URI) *workspacecommon.ISingleFolderWorkspaceIdentifier {
	return GetSingleFolderWorkspaceIdentifier(folderUri, nil)
}

// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
// NOTE: DO NOT CHANGE. IDENTIFIERS HAVE TO REMAIN STABLE
// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

// CreateEmptyWorkspaceIdentifier creates an empty workspace identifier
func CreateEmptyWorkspaceIdentifier() *workspacecommon.IEmptyWorkspaceIdentifier {
	// Use current timestamp plus random number for uniqueness
	timestamp := time.Now().UnixNano() / int64(time.Millisecond)
	randomNum := rand.Intn(1000)

	return &workspacecommon.IEmptyWorkspaceIdentifier{
		IBaseWorkspaceIdentifier: workspacecommon.IBaseWorkspaceIdentifier{
			ID: strconv.FormatInt(timestamp, 10) + strconv.Itoa(randomNum),
		},
	}
}
